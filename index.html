<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2D冒险游戏</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@7.x/dist/pixi.min.js"></script>
    <style>
        .map-selector {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }
        .map-selector select {
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        .map-selector button {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .map-selector button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div id="gameContainer"></div>

    <!-- 地图选择器 -->
    <div class="map-selector">
        <select id="mapSelector">
            <option value="">默认地图</option>
            <!-- 地图选项将通过JavaScript动态加载 -->
        </select>
        <button id="loadMapBtn">加载地图</button>
    </div>

    <!-- 数据库管理 -->
    <script src="js/database-manager.js"></script>
    <script src="js/sprite-manager.js"></script>
    <script src="js/entity-manager.js"></script>

    <!-- 战斗系统 -->
    <script src="js/collision-system.js"></script>
    <script src="js/combat-system.js"></script>

    <!-- 游戏核心 -->
    <script src="js/map.js"></script>
    <script src="js/player.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/game.js"></script>

    <!-- 初始化游戏 -->
    <script>
        // 创建全局游戏实例
        window.onload = function() {
            // 创建游戏实例并保存到全局变量
            window.game = new Game();

            // 加载地图列表
            loadMapList();

            // 设置地图加载按钮事件
            document.getElementById('loadMapBtn').addEventListener('click', function() {
                const mapId = document.getElementById('mapSelector').value;
                if (mapId) {
                    // 重新加载游戏并使用选定的地图
                    window.location.href = `index.html?map=${mapId}`;
                } else {
                    // 使用默认地图
                    window.location.href = `index.html`;
                }
            });

            // 添加调试信息
            console.log('游戏初始化完成，全局游戏实例已创建');
        };

        // 加载地图列表
        async function loadMapList() {
            try {
                // 访问游戏实例中的数据库管理器
                const dbManager = window.game.dbManager;
                // 获取所有地图
                const maps = await dbManager.getAllData(dbManager.stores.maps);

                // 获取地图选择下拉框
                const mapSelector = document.getElementById('mapSelector');

                // 添加地图选项
                maps.forEach(map => {
                    const option = document.createElement('option');
                    option.value = map.id;
                    option.textContent = map.name;
                    mapSelector.appendChild(option);
                });

                // 检查URL中是否有地图ID参数，如果有，设置为选中状态
                const urlParams = new URLSearchParams(window.location.search);
                const mapId = urlParams.get('map');
                if (mapId) {
                    mapSelector.value = mapId;
                }
            } catch (error) {
                console.error('加载地图列表失败:', error);
            }
        }
    </script>

    <!-- 游戏控制按钮 -->
    <div class="game-controls">
        <button id="adminBtn" onclick="window.open('admin.html', '_blank')">管理后台</button>
    </div>
</body>
</html>
