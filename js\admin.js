/**
 * 游戏管理后台脚本
 */
document.addEventListener('DOMContentLoaded', async function() {
    // 初始化管理器
    const dbManager = new DatabaseManager();
    const spriteManager = new SpriteManager(dbManager);
    const entityManager = new EntityManager(dbManager, spriteManager);
    const mapManager = new MapManager(dbManager);

    // 地图编辑器
    let mapEditor = null;

    // 等待数据库初始化
    try {
        await dbManager.initDatabase();
        await spriteManager.initialize();
        await entityManager.initialize();
        await mapManager.initialize();

        // 初始化地图编辑器
        mapEditor = new MapEditor(mapManager, entityManager);

        console.log('管理系统初始化完成');

        // 更新仪表盘统计数据
        updateDashboardStats();
    } catch (error) {
        console.error('管理系统初始化失败:', error);
        showNotification('初始化失败，请刷新页面重试', 'error');
    }

    // 导航菜单切换
    const navLinks = document.querySelectorAll('.admin-nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 检查是否是外部链接
            const isExternalLink = this.getAttribute('target') === '_blank';
            const sectionId = this.getAttribute('data-section');

            // 如果是外部链接，不阻止默认行为，直接返回
            if (isExternalLink || !sectionId) {
                return;
            }

            // 阻止默认行为
            e.preventDefault();

            // 移除所有活动状态
            navLinks.forEach(l => l.classList.remove('active'));
            document.querySelectorAll('.admin-section').forEach(section => section.classList.remove('active'));

            // 设置当前活动状态
            this.classList.add('active');

            // 获取对应的部分
            const section = document.getElementById(sectionId);

            // 确保找到了对应的部分
            if (section) {
                section.classList.add('active');

                // 加载部分数据
                loadSectionData(sectionId);
            } else {
                console.warn(`找不到ID为${sectionId}的部分`);
            }
        });
    });

    // 添加事件监听器的安全函数
    function addClickListener(id, callback) {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('click', callback);
        } else {
            console.warn(`找不到ID为${id}的元素`);
        }
    }

    // 快速操作按钮
    addClickListener('add-player-btn', () => showPlayerForm());
    addClickListener('add-enemy-btn', () => showEnemyForm());
    addClickListener('add-sprite-btn', () => showSpriteForm());
    addClickListener('add-animation-btn', () => showAnimationForm());

    // 部分操作按钮
    addClickListener('create-player-btn', () => showPlayerForm());
    addClickListener('create-enemy-btn', () => showEnemyForm());
    addClickListener('create-sprite-btn', () => showSpriteForm());
    addClickListener('create-animation-btn', () => showAnimationForm());

    // 游戏设置表单提交
    const settingsForm = document.getElementById('game-settings-form');
    if (settingsForm) {
        settingsForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await saveGameSettings();
        });
    }

    // 模态框关闭按钮
    addClickListener('modal-close', closeModal);

    // 动画预览控制
    addClickListener('preview-play', playAnimationPreview);
    addClickListener('preview-stop', stopAnimationPreview);
    addClickListener('preview-close', closeAnimationPreview);

    /**
     * 更新仪表盘统计数据
     */
    async function updateDashboardStats() {
        try {
            const players = await dbManager.getAllData(dbManager.stores.players);
            const enemies = await dbManager.getAllData(dbManager.stores.enemies);
            const sprites = await dbManager.getAllData(dbManager.stores.sprites);
            const animations = await dbManager.getAllData(dbManager.stores.animations);
            const maps = await dbManager.getAllData(dbManager.stores.maps);

            // 安全地更新DOM元素
            function updateElementText(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }

            updateElementText('player-count', players.length);
            updateElementText('enemy-count', enemies.length);
            updateElementText('map-count', maps.length);
            updateElementText('sprite-count', sprites.length);
            updateElementText('animation-count', animations.length);

            console.log('仪表盘统计数据更新成功');
        } catch (error) {
            console.error('更新仪表盘统计失败:', error);
        }
    }

    /**
     * 加载部分数据
     * @param {string} sectionId 部分ID
     */
    async function loadSectionData(sectionId) {
        // 检查sectionId是否有效
        if (!sectionId) {
            console.warn('无效的部分ID');
            return;
        }

        try {
            console.log(`加载部分数据: ${sectionId}`);

            switch (sectionId) {
                case 'dashboard':
                    // 仪表盘只需要更新统计数据
                    await updateDashboardStats();
                    break;
                case 'players':
                    await loadPlayers();
                    break;
                case 'enemies':
                    await loadEnemies();
                    break;
                case 'sprites':
                    await loadSprites();
                    break;
                case 'animations':
                    await loadAnimations();
                    break;
                case 'maps':
                    if (mapEditor) {
                        await mapEditor.loadMapList();
                    }
                    break;
                case 'settings':
                    await loadGameSettings();
                    break;
                default:
                    console.warn(`未知的部分ID: ${sectionId}`);
                    break;
            }
        } catch (error) {
            console.error(`加载${sectionId}数据失败:`, error);
            showNotification(`加载数据失败: ${error.message}`, 'error');
        }
    }

    /**
     * 加载玩家数据
     */
    async function loadPlayers() {
        const players = await dbManager.getAllData(dbManager.stores.players);
        const tableBody = document.querySelector('#players-table tbody');
        tableBody.innerHTML = '';

        if (players.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="7" class="text-center">没有玩家数据</td>';
            tableBody.appendChild(row);
            return;
        }

        players.forEach(player => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${player.id}</td>
                <td>${player.name}</td>
                <td>${player.health}/${player.maxHealth}</td>
                <td>${player.attack}</td>
                <td>${player.defense}</td>
                <td>${player.speed}</td>
                <td>
                    <button class="btn btn-small btn-secondary edit-player" data-id="${player.id}">编辑</button>
                    <button class="btn btn-small btn-danger delete-player" data-id="${player.id}">删除</button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // 添加事件监听器
        document.querySelectorAll('.edit-player').forEach(btn => {
            btn.addEventListener('click', function() {
                const playerId = parseInt(this.getAttribute('data-id'));
                showPlayerForm(playerId);
            });
        });

        document.querySelectorAll('.delete-player').forEach(btn => {
            btn.addEventListener('click', function() {
                const playerId = parseInt(this.getAttribute('data-id'));
                confirmDelete('player', playerId);
            });
        });
    }

    /**
     * 加载敌人数据
     */
    async function loadEnemies() {
        const enemies = await dbManager.getAllData(dbManager.stores.enemies);
        const tableBody = document.querySelector('#enemies-table tbody');
        tableBody.innerHTML = '';

        if (enemies.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="7" class="text-center">没有敌人数据</td>';
            tableBody.appendChild(row);
            return;
        }

        enemies.forEach(enemy => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${enemy.id}</td>
                <td>${enemy.name}</td>
                <td>${enemy.health}/${enemy.maxHealth}</td>
                <td>${enemy.attack}</td>
                <td>${enemy.defense}</td>
                <td>${enemy.speed}</td>
                <td>
                    <button class="btn btn-small btn-secondary edit-enemy" data-id="${enemy.id}">编辑</button>
                    <button class="btn btn-small btn-danger delete-enemy" data-id="${enemy.id}">删除</button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // 添加事件监听器
        document.querySelectorAll('.edit-enemy').forEach(btn => {
            btn.addEventListener('click', function() {
                const enemyId = parseInt(this.getAttribute('data-id'));
                showEnemyForm(enemyId);
            });
        });

        document.querySelectorAll('.delete-enemy').forEach(btn => {
            btn.addEventListener('click', function() {
                const enemyId = parseInt(this.getAttribute('data-id'));
                confirmDelete('enemy', enemyId);
            });
        });
    }

    /**
     * 加载精灵表数据
     */
    async function loadSprites() {
        const sprites = await dbManager.getAllData(dbManager.stores.sprites);
        const container = document.getElementById('sprites-container');
        container.innerHTML = '';

        if (sprites.length === 0) {
            container.innerHTML = '<p class="text-center">没有精灵表数据</p>';
            return;
        }

        sprites.forEach(sprite => {
            const card = document.createElement('div');
            card.className = 'sprite-card';
            card.innerHTML = `
                <div class="sprite-preview">
                    <img src="${sprite.imagePath}" alt="${sprite.name}">
                </div>
                <div class="sprite-info">
                    <h4>${sprite.name}</h4>
                    <p>尺寸: ${sprite.frameWidth}x${sprite.frameHeight}</p>
                    <p>帧数: ${sprite.columns}x${sprite.rows}</p>
                </div>
                <div class="sprite-actions">
                    <button class="btn btn-small btn-secondary edit-sprite" data-id="${sprite.id}">编辑</button>
                    <button class="btn btn-small btn-danger delete-sprite" data-id="${sprite.id}">删除</button>
                </div>
            `;
            container.appendChild(card);
        });

        // 添加事件监听器
        document.querySelectorAll('.edit-sprite').forEach(btn => {
            btn.addEventListener('click', function() {
                const spriteId = parseInt(this.getAttribute('data-id'));
                showSpriteForm(spriteId);
            });
        });

        document.querySelectorAll('.delete-sprite').forEach(btn => {
            btn.addEventListener('click', function() {
                const spriteId = parseInt(this.getAttribute('data-id'));
                confirmDelete('sprite', spriteId);
            });
        });
    }

    /**
     * 加载动画数据
     */
    async function loadAnimations() {
        const animations = await dbManager.getAllData(dbManager.stores.animations);
        const sprites = await dbManager.getAllData(dbManager.stores.sprites);
        const tableBody = document.querySelector('#animations-table tbody');
        tableBody.innerHTML = '';

        if (animations.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="7" class="text-center">没有动画数据</td>';
            tableBody.appendChild(row);
            return;
        }

        // 创建精灵表映射
        const spriteMap = {};
        sprites.forEach(sprite => {
            spriteMap[sprite.id] = sprite.name;
        });

        animations.forEach(animation => {
            const row = document.createElement('tr');
            const spriteName = spriteMap[animation.spriteId] || '未知';
            const frameCount = Object.values(animation.frames).reduce((total, frames) => total + frames.length, 0);

            row.innerHTML = `
                <td>${animation.id}</td>
                <td>${animation.name}</td>
                <td>${spriteName}</td>
                <td>${animation.type}</td>
                <td>${frameCount}</td>
                <td>${animation.speed}</td>
                <td>
                    <button class="btn btn-small btn-secondary edit-animation" data-id="${animation.id}">编辑</button>
                    <button class="btn btn-small btn-primary preview-animation" data-id="${animation.id}">预览</button>
                    <button class="btn btn-small btn-danger delete-animation" data-id="${animation.id}">删除</button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // 添加事件监听器
        document.querySelectorAll('.edit-animation').forEach(btn => {
            btn.addEventListener('click', function() {
                const animationId = parseInt(this.getAttribute('data-id'));
                showAnimationForm(animationId);
            });
        });

        document.querySelectorAll('.preview-animation').forEach(btn => {
            btn.addEventListener('click', function() {
                const animationId = parseInt(this.getAttribute('data-id'));
                showAnimationPreview(animationId);
            });
        });

        document.querySelectorAll('.delete-animation').forEach(btn => {
            btn.addEventListener('click', function() {
                const animationId = parseInt(this.getAttribute('data-id'));
                confirmDelete('animation', animationId);
            });
        });
    }

    /**
     * 加载游戏设置
     */
    async function loadGameSettings() {
        try {
            const settings = await dbManager.getAllData(dbManager.stores.gameSettings);
            const players = await dbManager.getAllData(dbManager.stores.players);

            // 填充玩家选择框
            const playerSelect = document.getElementById('default-player');
            if (!playerSelect) {
                console.warn('找不到默认玩家选择框元素');
                return;
            }

            playerSelect.innerHTML = '<option value="">选择默认玩家</option>';

            players.forEach(player => {
                const option = document.createElement('option');
                option.value = player.id;
                option.textContent = player.name;
                playerSelect.appendChild(option);
            });

            // 如果有设置，填充表单
            if (settings.length > 0) {
                const setting = settings[0]; // 使用第一个设置

                // 安全地设置表单值
                function setInputValue(id, value) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = value;
                    } else {
                        console.warn(`找不到ID为${id}的元素`);
                    }
                }

                setInputValue('game-width', setting.width || 1280);
                setInputValue('game-height', setting.height || 900);
                setInputValue('enemy-spawn-rate', setting.enemySpawnRate || 5);

                if (setting.defaultPlayer) {
                    setInputValue('default-player', setting.defaultPlayer);
                }
            }

            console.log('游戏设置加载成功');
        } catch (error) {
            console.error('加载游戏设置失败:', error);
            showNotification('加载游戏设置失败', 'error');
        }
    }

    /**
     * 保存游戏设置
     */
    async function saveGameSettings() {
        try {
            // 安全地获取表单值
            function getInputValue(id, defaultValue, parser = (v) => v) {
                const element = document.getElementById(id);
                if (element) {
                    return parser(element.value);
                } else {
                    console.warn(`找不到ID为${id}的元素，使用默认值`);
                    return defaultValue;
                }
            }

            const formData = {
                width: getInputValue('game-width', 1280, parseInt),
                height: getInputValue('game-height', 900, parseInt),
                defaultPlayer: getInputValue('default-player', ''),
                enemySpawnRate: getInputValue('enemy-spawn-rate', 5, parseFloat)
            };

            console.log('保存的游戏设置:', formData);

            // 检查是否已有设置
            const settings = await dbManager.getAllData(dbManager.stores.gameSettings);

            if (settings.length > 0) {
                // 更新现有设置
                const setting = settings[0];
                setting.width = formData.width;
                setting.height = formData.height;
                setting.defaultPlayer = formData.defaultPlayer;
                setting.enemySpawnRate = formData.enemySpawnRate;

                await dbManager.updateData(dbManager.stores.gameSettings, setting);
                console.log('更新现有游戏设置成功');
            } else {
                // 创建新设置
                await dbManager.addData(dbManager.stores.gameSettings, formData);
                console.log('创建新游戏设置成功');
            }

            showNotification('游戏设置已保存', 'success');
        } catch (error) {
            console.error('保存游戏设置失败:', error);
            showNotification('保存游戏设置失败', 'error');
        }
    }

    /**
     * 显示玩家表单
     * @param {number} playerId 玩家ID，如果是新建则为null
     */
    async function showPlayerForm(playerId = null) {
        try {
            let player = null;
            let animations = await dbManager.getAllData(dbManager.stores.animations);
            // 获取精灵表来确定动画类型
            const sprites = await dbManager.getAllData(dbManager.stores.sprites);
            const spriteMap = {};
            sprites.forEach(sprite => {
                spriteMap[sprite.id] = sprite.type;
            });

            // 筛选出玩家类型的动画
            animations = animations.filter(animation => {
                const spriteType = spriteMap[animation.spriteId] || '';
                return spriteType === 'player' || spriteType === '';
            });

            if (playerId) {
                player = await dbManager.getData(dbManager.stores.players, playerId);
            }

            // 创建表单HTML
            const formHtml = `
                <form id="player-form" class="entity-form">
                    <input type="hidden" name="id" value="${player ? player.id : ''}">
                    <input type="hidden" name="type" value="player">

                    <div class="form-group">
                        <label for="player-name">名称</label>
                        <input type="text" id="player-name" name="name" value="${player ? player.name : ''}" required>
                    </div>

                    <div class="form-group">
                        <label for="player-health">生命值</label>
                        <input type="number" id="player-health" name="health" min="1" value="${player ? player.health : 100}" required>
                    </div>

                    <div class="form-group">
                        <label for="player-max-health">最大生命值</label>
                        <input type="number" id="player-max-health" name="maxHealth" min="1" value="${player ? player.maxHealth : 100}" required>
                    </div>

                    <div class="form-group">
                        <label for="player-attack">攻击力</label>
                        <input type="number" id="player-attack" name="attack" min="0" value="${player ? player.attack : 10}" required>
                    </div>

                    <div class="form-group">
                        <label for="player-defense">防御力</label>
                        <input type="number" id="player-defense" name="defense" min="0" value="${player ? player.defense : 5}" required>
                    </div>

                    <div class="form-group">
                        <label for="player-speed">速度</label>
                        <input type="number" id="player-speed" name="speed" min="1" step="0.1" value="${player ? player.speed : 3}" required>
                    </div>

                    <h4>默认动画尺寸设置</h4>
                    <div class="form-group">
                        <label for="player-default-width">默认宽度</label>
                        <input type="number" id="player-default-width" name="defaultAnimationWidth" min="1" value="${player && player.defaultAnimationWidth ? player.defaultAnimationWidth : 60}" required>
                    </div>

                    <div class="form-group">
                        <label for="player-default-height">默认高度</label>
                        <input type="number" id="player-default-height" name="defaultAnimationHeight" min="1" value="${player && player.defaultAnimationHeight ? player.defaultAnimationHeight : 60}" required>
                    </div>

                    <h4>碰撞和范围设置</h4>
                    <div class="collision-range-settings">
                        <div class="form-group">
                            <label for="player-collision-width">碰撞框宽度</label>
                            <input type="number" id="player-collision-width" name="collisionBox.width" min="1" value="${player && player.collisionBox ? player.collisionBox.width : 40}" required>
                        </div>
                        <div class="form-group">
                            <label for="player-collision-height">碰撞框高度</label>
                            <input type="number" id="player-collision-height" name="collisionBox.height" min="1" value="${player && player.collisionBox ? player.collisionBox.height : 40}" required>
                        </div>
                        <div class="form-group">
                            <label for="player-attack-range-width">攻击范围宽度</label>
                            <input type="number" id="player-attack-range-width" name="attackRange.width" min="1" value="${player && player.attackRange ? player.attackRange.width : 80}" required>
                        </div>
                        <div class="form-group">
                            <label for="player-attack-range-height">攻击范围高度</label>
                            <input type="number" id="player-attack-range-height" name="attackRange.height" min="1" value="${player && player.attackRange ? player.attackRange.height : 80}" required>
                        </div>
                        <div class="form-group">
                            <button type="button" id="preview-player-ranges" class="btn btn-secondary">预览范围</button>
                        </div>
                    </div>

                    <h4>动画设置</h4>

                    <div class="animation-settings-container">
                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="player-idle-animation">待机动画</label>
                                <select id="player-idle-animation" name="animations.idle">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'idle').map(a =>
                                        `<option value="${a.id}" ${player && player.animations && player.animations.idle == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="player-idle-width">宽度</label>
                                    <input type="number" id="player-idle-width" name="animationSizes.idle.width" min="1" value="${player && player.animationSizes && player.animationSizes.idle ? player.animationSizes.idle.width : 60}">
                                </div>
                                <div class="form-group">
                                    <label for="player-idle-height">高度</label>
                                    <input type="number" id="player-idle-height" name="animationSizes.idle.height" min="1" value="${player && player.animationSizes && player.animationSizes.idle ? player.animationSizes.idle.height : 60}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="player-move-animation">移动动画</label>
                                <select id="player-move-animation" name="animations.move">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'move').map(a =>
                                        `<option value="${a.id}" ${player && player.animations && player.animations.move == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="player-move-width">宽度</label>
                                    <input type="number" id="player-move-width" name="animationSizes.move.width" min="1" value="${player && player.animationSizes && player.animationSizes.move ? player.animationSizes.move.width : 60}">
                                </div>
                                <div class="form-group">
                                    <label for="player-move-height">高度</label>
                                    <input type="number" id="player-move-height" name="animationSizes.move.height" min="1" value="${player && player.animationSizes && player.animationSizes.move ? player.animationSizes.move.height : 60}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="player-attack-animation">攻击动画</label>
                                <select id="player-attack-animation" name="animations.attack">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'attack').map(a =>
                                        `<option value="${a.id}" ${player && player.animations && player.animations.attack == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="player-attack-width">宽度</label>
                                    <input type="number" id="player-attack-width" name="animationSizes.attack.width" min="1" value="${player && player.animationSizes && player.animationSizes.attack ? player.animationSizes.attack.width : 60}">
                                </div>
                                <div class="form-group">
                                    <label for="player-attack-height">高度</label>
                                    <input type="number" id="player-attack-height" name="animationSizes.attack.height" min="1" value="${player && player.animationSizes && player.animationSizes.attack ? player.animationSizes.attack.height : 60}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="player-skill-animation">技能动画</label>
                                <select id="player-skill-animation" name="animations.skill">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'skill').map(a =>
                                        `<option value="${a.id}" ${player && player.animations && player.animations.skill == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="player-skill-width">宽度</label>
                                    <input type="number" id="player-skill-width" name="animationSizes.skill.width" min="1" value="${player && player.animationSizes && player.animationSizes.skill ? player.animationSizes.skill.width : 60}">
                                </div>
                                <div class="form-group">
                                    <label for="player-skill-height">高度</label>
                                    <input type="number" id="player-skill-height" name="animationSizes.skill.height" min="1" value="${player && player.animationSizes && player.animationSizes.skill ? player.animationSizes.skill.height : 60}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="player-death-animation">死亡动画</label>
                                <select id="player-death-animation" name="animations.death">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'death').map(a =>
                                        `<option value="${a.id}" ${player && player.animations && player.animations.death == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="player-death-width">宽度</label>
                                    <input type="number" id="player-death-width" name="animationSizes.death.width" min="1" value="${player && player.animationSizes && player.animationSizes.death ? player.animationSizes.death.width : 60}">
                                </div>
                                <div class="form-group">
                                    <label for="player-death-height">高度</label>
                                    <input type="number" id="player-death-height" name="animationSizes.death.height" min="1" value="${player && player.animationSizes && player.animationSizes.death ? player.animationSizes.death.height : 60}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="player-revive-animation">复活动画</label>
                                <select id="player-revive-animation" name="animations.revive">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'revive').map(a =>
                                        `<option value="${a.id}" ${player && player.animations && player.animations.revive == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="player-revive-width">宽度</label>
                                    <input type="number" id="player-revive-width" name="animationSizes.revive.width" min="1" value="${player && player.animationSizes && player.animationSizes.revive ? player.animationSizes.revive.width : 60}">
                                </div>
                                <div class="form-group">
                                    <label for="player-revive-height">高度</label>
                                    <input type="number" id="player-revive-height" name="animationSizes.revive.height" min="1" value="${player && player.animationSizes && player.animationSizes.revive ? player.animationSizes.revive.height : 60}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">${player ? '更新' : '创建'}</button>
                        <button type="button" class="btn btn-secondary" id="cancel-player-form">取消</button>
                    </div>
                </form>
            `;

            // 显示模态框
            showModal(player ? '编辑玩家' : '创建新玩家', formHtml);

            // 添加表单提交事件
            document.getElementById('player-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                await savePlayerData(this);
            });

            // 添加取消按钮事件
            document.getElementById('cancel-player-form').addEventListener('click', closeModal);

            // 添加预览按钮事件
            document.getElementById('preview-player-ranges').addEventListener('click', function() {
                showEntityRangePreview('player');
            });

            // 添加实时预览功能
            setupRealTimePreview('player');

        } catch (error) {
            console.error('显示玩家表单失败:', error);
            showNotification('显示玩家表单失败', 'error');
        }
    }

    /**
     * 保存玩家数据
     * @param {HTMLFormElement} form 表单元素
     */
    async function savePlayerData(form) {
        try {
            const formData = new FormData(form);
            const playerData = {
                name: formData.get('name'),
                type: formData.get('type'),
                health: parseInt(formData.get('health')),
                maxHealth: parseInt(formData.get('maxHealth')),
                attack: parseInt(formData.get('attack')),
                defense: parseInt(formData.get('defense')),
                speed: parseFloat(formData.get('speed')),
                defaultAnimationWidth: parseInt(formData.get('defaultAnimationWidth')),
                defaultAnimationHeight: parseInt(formData.get('defaultAnimationHeight')),
                collisionBox: {
                    width: parseInt(formData.get('collisionBox.width')),
                    height: parseInt(formData.get('collisionBox.height'))
                },
                attackRange: {
                    width: parseInt(formData.get('attackRange.width')),
                    height: parseInt(formData.get('attackRange.height'))
                },
                animations: {},
                animationSizes: {}
            };

            // 处理动画数据
            const animationTypes = ['idle', 'move', 'attack', 'skill', 'death', 'revive'];
            animationTypes.forEach(type => {
                const animId = formData.get(`animations.${type}`);
                if (animId) {
                    playerData.animations[type] = parseInt(animId);

                    // 处理对应动画的尺寸
                    const width = formData.get(`animationSizes.${type}.width`);
                    const height = formData.get(`animationSizes.${type}.height`);

                    if (width && height) {
                        playerData.animationSizes[type] = {
                            width: parseInt(width),
                            height: parseInt(height)
                        };
                    }
                }
            });

            // 检查是新建还是更新
            const playerId = formData.get('id');

            if (playerId) {
                // 更新
                playerData.id = parseInt(playerId);
                await entityManager.updatePlayer(playerData);
                showNotification('玩家更新成功', 'success');
            } else {
                // 新建
                await entityManager.createPlayer(playerData);
                showNotification('玩家创建成功', 'success');
            }

            // 关闭模态框并刷新数据
            closeModal();
            loadPlayers();
            updateDashboardStats();

        } catch (error) {
            console.error('保存玩家数据失败:', error);
            showNotification('保存玩家数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示敌人表单
     * @param {number} enemyId 敌人ID，如果是新建则为null
     */
    async function showEnemyForm(enemyId = null) {
        try {
            let enemy = null;
            let animations = await dbManager.getAllData(dbManager.stores.animations);
            // 获取精灵表来确定动画类型
            const sprites = await dbManager.getAllData(dbManager.stores.sprites);
            const spriteMap = {};
            sprites.forEach(sprite => {
                spriteMap[sprite.id] = sprite.type;
            });

            // 筛选出敌人类型的动画
            animations = animations.filter(animation => {
                const spriteType = spriteMap[animation.spriteId] || '';
                return spriteType === 'enemy' || spriteType === '';
            });

            if (enemyId) {
                enemy = await dbManager.getData(dbManager.stores.enemies, enemyId);
            }

            // 创建表单HTML
            const formHtml = `
                <form id="enemy-form" class="entity-form">
                    <input type="hidden" name="id" value="${enemy ? enemy.id : ''}">
                    <input type="hidden" name="type" value="enemy">

                    <div class="form-group">
                        <label for="enemy-name">名称</label>
                        <input type="text" id="enemy-name" name="name" value="${enemy ? enemy.name : ''}" required>
                    </div>

                    <div class="form-group">
                        <label for="enemy-health">生命值</label>
                        <input type="number" id="enemy-health" name="health" min="1" value="${enemy ? enemy.health : 50}" required>
                    </div>

                    <div class="form-group">
                        <label for="enemy-max-health">最大生命值</label>
                        <input type="number" id="enemy-max-health" name="maxHealth" min="1" value="${enemy ? enemy.maxHealth : 50}" required>
                    </div>

                    <div class="form-group">
                        <label for="enemy-attack">攻击力</label>
                        <input type="number" id="enemy-attack" name="attack" min="0" value="${enemy ? enemy.attack : 5}" required>
                    </div>

                    <div class="form-group">
                        <label for="enemy-defense">防御力</label>
                        <input type="number" id="enemy-defense" name="defense" min="0" value="${enemy ? enemy.defense : 2}" required>
                    </div>

                    <div class="form-group">
                        <label for="enemy-speed">速度</label>
                        <input type="number" id="enemy-speed" name="speed" min="0.1" step="0.1" value="${enemy ? enemy.speed : 2}" required>
                    </div>

                    <h4>默认动画尺寸设置</h4>
                    <div class="form-group">
                        <label for="enemy-default-width">默认宽度</label>
                        <input type="number" id="enemy-default-width" name="defaultAnimationWidth" min="1" value="${enemy && enemy.defaultAnimationWidth ? enemy.defaultAnimationWidth : 50}" required>
                    </div>

                    <div class="form-group">
                        <label for="enemy-default-height">默认高度</label>
                        <input type="number" id="enemy-default-height" name="defaultAnimationHeight" min="1" value="${enemy && enemy.defaultAnimationHeight ? enemy.defaultAnimationHeight : 50}" required>
                    </div>

                    <h4>碰撞和范围设置</h4>
                    <div class="collision-range-settings">
                        <div class="form-group">
                            <label for="enemy-collision-width">碰撞框宽度</label>
                            <input type="number" id="enemy-collision-width" name="collisionBox.width" min="1" value="${enemy && enemy.collisionBox ? enemy.collisionBox.width : 30}" required>
                        </div>
                        <div class="form-group">
                            <label for="enemy-collision-height">碰撞框高度</label>
                            <input type="number" id="enemy-collision-height" name="collisionBox.height" min="1" value="${enemy && enemy.collisionBox ? enemy.collisionBox.height : 30}" required>
                        </div>
                        <div class="form-group">
                            <label for="enemy-attack-range-width">攻击范围宽度</label>
                            <input type="number" id="enemy-attack-range-width" name="attackRange.width" min="1" value="${enemy && enemy.attackRange ? enemy.attackRange.width : 60}" required>
                        </div>
                        <div class="form-group">
                            <label for="enemy-attack-range-height">攻击范围高度</label>
                            <input type="number" id="enemy-attack-range-height" name="attackRange.height" min="1" value="${enemy && enemy.attackRange ? enemy.attackRange.height : 60}" required>
                        </div>
                        <div class="form-group">
                            <label for="enemy-alert-range-width">警戒范围宽度</label>
                            <input type="number" id="enemy-alert-range-width" name="alertRange.width" min="1" value="${enemy && enemy.alertRange ? enemy.alertRange.width : 120}" required>
                        </div>
                        <div class="form-group">
                            <label for="enemy-alert-range-height">警戒范围高度</label>
                            <input type="number" id="enemy-alert-range-height" name="alertRange.height" min="1" value="${enemy && enemy.alertRange ? enemy.alertRange.height : 120}" required>
                        </div>
                        <div class="form-group">
                            <button type="button" id="preview-enemy-ranges" class="btn btn-secondary">预览范围</button>
                        </div>
                    </div>

                    <h4>动画设置</h4>

                    <div class="animation-settings-container">
                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="enemy-idle-animation">待机动画</label>
                                <select id="enemy-idle-animation" name="animations.idle">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'idle').map(a =>
                                        `<option value="${a.id}" ${enemy && enemy.animations && enemy.animations.idle == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="enemy-idle-width">宽度</label>
                                    <input type="number" id="enemy-idle-width" name="animationSizes.idle.width" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.idle ? enemy.animationSizes.idle.width : 50}">
                                </div>
                                <div class="form-group">
                                    <label for="enemy-idle-height">高度</label>
                                    <input type="number" id="enemy-idle-height" name="animationSizes.idle.height" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.idle ? enemy.animationSizes.idle.height : 50}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="enemy-move-animation">移动动画</label>
                                <select id="enemy-move-animation" name="animations.move">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'move').map(a =>
                                        `<option value="${a.id}" ${enemy && enemy.animations && enemy.animations.move == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="enemy-move-width">宽度</label>
                                    <input type="number" id="enemy-move-width" name="animationSizes.move.width" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.move ? enemy.animationSizes.move.width : 50}">
                                </div>
                                <div class="form-group">
                                    <label for="enemy-move-height">高度</label>
                                    <input type="number" id="enemy-move-height" name="animationSizes.move.height" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.move ? enemy.animationSizes.move.height : 50}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="enemy-attack-animation">攻击动画</label>
                                <select id="enemy-attack-animation" name="animations.attack">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'attack').map(a =>
                                        `<option value="${a.id}" ${enemy && enemy.animations && enemy.animations.attack == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="enemy-attack-width">宽度</label>
                                    <input type="number" id="enemy-attack-width" name="animationSizes.attack.width" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.attack ? enemy.animationSizes.attack.width : 50}">
                                </div>
                                <div class="form-group">
                                    <label for="enemy-attack-height">高度</label>
                                    <input type="number" id="enemy-attack-height" name="animationSizes.attack.height" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.attack ? enemy.animationSizes.attack.height : 50}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="enemy-skill-animation">技能动画</label>
                                <select id="enemy-skill-animation" name="animations.skill">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'skill').map(a =>
                                        `<option value="${a.id}" ${enemy && enemy.animations && enemy.animations.skill == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="enemy-skill-width">宽度</label>
                                    <input type="number" id="enemy-skill-width" name="animationSizes.skill.width" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.skill ? enemy.animationSizes.skill.width : 50}">
                                </div>
                                <div class="form-group">
                                    <label for="enemy-skill-height">高度</label>
                                    <input type="number" id="enemy-skill-height" name="animationSizes.skill.height" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.skill ? enemy.animationSizes.skill.height : 50}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="enemy-death-animation">死亡动画</label>
                                <select id="enemy-death-animation" name="animations.death">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'death').map(a =>
                                        `<option value="${a.id}" ${enemy && enemy.animations && enemy.animations.death == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="enemy-death-width">宽度</label>
                                    <input type="number" id="enemy-death-width" name="animationSizes.death.width" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.death ? enemy.animationSizes.death.width : 50}">
                                </div>
                                <div class="form-group">
                                    <label for="enemy-death-height">高度</label>
                                    <input type="number" id="enemy-death-height" name="animationSizes.death.height" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.death ? enemy.animationSizes.death.height : 50}">
                                </div>
                            </div>
                        </div>

                        <div class="animation-settings-item">
                            <div class="form-group">
                                <label for="enemy-revive-animation">复活动画</label>
                                <select id="enemy-revive-animation" name="animations.revive">
                                    <option value="">无</option>
                                    ${animations.filter(a => a.type === 'revive').map(a =>
                                        `<option value="${a.id}" ${enemy && enemy.animations && enemy.animations.revive == a.id ? 'selected' : ''}>${a.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="animation-size-settings">
                                <div class="form-group">
                                    <label for="enemy-revive-width">宽度</label>
                                    <input type="number" id="enemy-revive-width" name="animationSizes.revive.width" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.revive ? enemy.animationSizes.revive.width : 50}">
                                </div>
                                <div class="form-group">
                                    <label for="enemy-revive-height">高度</label>
                                    <input type="number" id="enemy-revive-height" name="animationSizes.revive.height" min="1" value="${enemy && enemy.animationSizes && enemy.animationSizes.revive ? enemy.animationSizes.revive.height : 50}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">${enemy ? '更新' : '创建'}</button>
                        <button type="button" class="btn btn-secondary" id="cancel-enemy-form">取消</button>
                    </div>
                </form>
            `;

            // 显示模态框
            showModal(enemy ? '编辑敌人' : '创建新敌人', formHtml);

            // 添加表单提交事件
            document.getElementById('enemy-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                await saveEnemyData(this);
            });

            // 添加取消按钮事件
            document.getElementById('cancel-enemy-form').addEventListener('click', closeModal);

            // 添加预览按钮事件
            document.getElementById('preview-enemy-ranges').addEventListener('click', function() {
                showEntityRangePreview('enemy');
            });

            // 添加实时预览功能
            setupRealTimePreview('enemy');

        } catch (error) {
            console.error('显示敌人表单失败:', error);
            showNotification('显示敌人表单失败', 'error');
        }
    }

    /**
     * 保存敌人数据
     * @param {HTMLFormElement} form 表单元素
     */
    async function saveEnemyData(form) {
        try {
            const formData = new FormData(form);
            const enemyData = {
                name: formData.get('name'),
                type: formData.get('type'),
                health: parseInt(formData.get('health')),
                maxHealth: parseInt(formData.get('maxHealth')),
                attack: parseInt(formData.get('attack')),
                defense: parseInt(formData.get('defense')),
                speed: parseFloat(formData.get('speed')),
                defaultAnimationWidth: parseInt(formData.get('defaultAnimationWidth')),
                defaultAnimationHeight: parseInt(formData.get('defaultAnimationHeight')),
                collisionBox: {
                    width: parseInt(formData.get('collisionBox.width')),
                    height: parseInt(formData.get('collisionBox.height'))
                },
                attackRange: {
                    width: parseInt(formData.get('attackRange.width')),
                    height: parseInt(formData.get('attackRange.height'))
                },
                alertRange: {
                    width: parseInt(formData.get('alertRange.width')),
                    height: parseInt(formData.get('alertRange.height'))
                },
                animations: {},
                animationSizes: {}
            };

            // 处理动画数据
            const animationTypes = ['idle', 'move', 'attack', 'skill', 'death', 'revive'];
            animationTypes.forEach(type => {
                const animId = formData.get(`animations.${type}`);
                if (animId) {
                    enemyData.animations[type] = parseInt(animId);

                    // 处理对应动画的尺寸
                    const width = formData.get(`animationSizes.${type}.width`);
                    const height = formData.get(`animationSizes.${type}.height`);

                    if (width && height) {
                        enemyData.animationSizes[type] = {
                            width: parseInt(width),
                            height: parseInt(height)
                        };
                    }
                }
            });

            // 检查是新建还是更新
            const enemyId = formData.get('id');

            if (enemyId) {
                // 更新
                enemyData.id = parseInt(enemyId);
                await entityManager.updateEnemy(enemyData);
                showNotification('敌人更新成功', 'success');
            } else {
                // 新建
                await entityManager.createEnemy(enemyData);
                showNotification('敌人创建成功', 'success');
            }

            // 关闭模态框并刷新数据
            closeModal();
            loadEnemies();
            updateDashboardStats();

        } catch (error) {
            console.error('保存敌人数据失败:', error);
            showNotification('保存敌人数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示精灵表表单
     * @param {number} spriteId 精灵表ID，如果是新建则为null
     */
    async function showSpriteForm(spriteId = null) {
        try {
            let sprite = null;

            if (spriteId) {
                sprite = await dbManager.getData(dbManager.stores.sprites, spriteId);
            }

            // 创建表单HTML
            const formHtml = `
                <form id="sprite-form" class="entity-form">
                    <input type="hidden" name="id" value="${sprite ? sprite.id : ''}">

                    <div class="form-group">
                        <label for="sprite-name">名称</label>
                        <input type="text" id="sprite-name" name="name" value="${sprite ? sprite.name : ''}" required>
                    </div>

                    <div class="form-group">
                        <label for="sprite-type">类型</label>
                        <select id="sprite-type" name="type" required>
                            <option value="player" ${sprite && sprite.type === 'player' ? 'selected' : ''}>玩家</option>
                            <option value="enemy" ${sprite && sprite.type === 'enemy' ? 'selected' : ''}>敌人</option>
                            <option value="item" ${sprite && sprite.type === 'item' ? 'selected' : ''}>物品</option>
                            <option value="effect" ${sprite && sprite.type === 'effect' ? 'selected' : ''}>特效</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="sprite-image">精灵表图片</label>
                        ${sprite ?
                            `<div class="sprite-preview">
                                <img src="${sprite.imagePath}" alt="${sprite.name}">
                            </div>` :
                            ''}
                        <input type="file" id="sprite-image" name="image" accept="image/*" ${sprite ? '' : 'required'}>
                        <input type="hidden" name="imagePath" value="${sprite ? sprite.imagePath : ''}">
                    </div>

                    <div class="form-group">
                        <label for="sprite-frame-width">帧宽度</label>
                        <input type="number" id="sprite-frame-width" name="frameWidth" min="1" value="${sprite ? sprite.frameWidth : 32}" required>
                    </div>

                    <div class="form-group">
                        <label for="sprite-frame-height">帧高度</label>
                        <input type="number" id="sprite-frame-height" name="frameHeight" min="1" value="${sprite ? sprite.frameHeight : 32}" required>
                    </div>

                    <div class="form-group">
                        <label for="sprite-columns">列数</label>
                        <input type="number" id="sprite-columns" name="columns" min="1" value="${sprite ? sprite.columns : 4}" required>
                    </div>

                    <div class="form-group">
                        <label for="sprite-rows">行数</label>
                        <input type="number" id="sprite-rows" name="rows" min="1" value="${sprite ? sprite.rows : 4}" required>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">${sprite ? '更新' : '上传'}</button>
                        <button type="button" class="btn btn-secondary" id="cancel-sprite-form">取消</button>
                    </div>
                </form>
            `;

            // 显示模态框
            showModal(sprite ? '编辑精灵表' : '上传新精灵表', formHtml);

            // 添加表单提交事件
            document.getElementById('sprite-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                await saveSpriteData(this);
            });

            // 添加取消按钮事件
            document.getElementById('cancel-sprite-form').addEventListener('click', closeModal);

        } catch (error) {
            console.error('显示精灵表表单失败:', error);
            showNotification('显示精灵表表单失败', 'error');
        }
    }

    /**
     * 保存精灵表数据
     * @param {HTMLFormElement} form 表单元素
     */
    async function saveSpriteData(form) {
        try {
            const formData = new FormData(form);
            const spriteData = {
                name: formData.get('name'),
                type: formData.get('type'),
                frameWidth: parseInt(formData.get('frameWidth')),
                frameHeight: parseInt(formData.get('frameHeight')),
                columns: parseInt(formData.get('columns')),
                rows: parseInt(formData.get('rows'))
            };

            // 处理图片
            const imageFile = formData.get('image');
            let imagePath = formData.get('imagePath');

            if (imageFile && imageFile.size > 0) {
                try {
                    // 将文件转换为Base64字符串
                    const base64Image = await convertFileToBase64(imageFile);

                    // 使用Base64字符串作为图像路径
                    imagePath = base64Image;

                    console.log('图像已转换为Base64格式');
                } catch (error) {
                    console.error('图像转换失败:', error);
                    throw new Error('图像处理失败: ' + error.message);
                }
            }

            if (!imagePath) {
                throw new Error('请选择精灵表图片');
            }

            spriteData.imagePath = imagePath;

            // 检查是新建还是更新
            const spriteId = formData.get('id');

            if (spriteId) {
                // 更新
                spriteData.id = parseInt(spriteId);
                await spriteManager.updateSprite(spriteData);
                showNotification('精灵表更新成功', 'success');
            } else {
                // 新建
                await spriteManager.createSprite(spriteData);
                showNotification('精灵表上传成功', 'success');
            }

            // 关闭模态框并刷新数据
            closeModal();
            loadSprites();
            updateDashboardStats();

        } catch (error) {
            console.error('保存精灵表数据失败:', error);
            showNotification('保存精灵表数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示动画表单
     * @param {number} animationId 动画ID，如果是新建则为null
     */
    async function showAnimationForm(animationId = null) {
        try {
            let animation = null;
            const sprites = await dbManager.getAllData(dbManager.stores.sprites);

            if (sprites.length === 0) {
                showNotification('请先上传精灵表', 'error');
                return;
            }

            if (animationId) {
                animation = await dbManager.getData(dbManager.stores.animations, animationId);
            }

            // 创建表单HTML
            const formHtml = `
                <form id="animation-form" class="entity-form">
                    <input type="hidden" name="id" value="${animation ? animation.id : ''}">

                    <div class="form-group">
                        <label for="animation-name">名称</label>
                        <input type="text" id="animation-name" name="name" value="${animation ? animation.name : ''}" required>
                    </div>

                    <div class="form-group">
                        <label for="animation-type">类型</label>
                        <select id="animation-type" name="type" required>
                            <option value="idle" ${animation && animation.type === 'idle' ? 'selected' : ''}>待机</option>
                            <option value="move" ${animation && animation.type === 'move' ? 'selected' : ''}>移动</option>
                            <option value="attack" ${animation && animation.type === 'attack' ? 'selected' : ''}>攻击</option>
                            <option value="skill" ${animation && animation.type === 'skill' ? 'selected' : ''}>技能</option>
                            <option value="death" ${animation && animation.type === 'death' ? 'selected' : ''}>死亡</option>
                            <option value="revive" ${animation && animation.type === 'revive' ? 'selected' : ''}>复活</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="animation-sprite">精灵表</label>
                        <select id="animation-sprite" name="spriteId" required>
                            <option value="">选择精灵表</option>
                            ${sprites.map(sprite =>
                                `<option value="${sprite.id}" ${animation && animation.spriteId == sprite.id ? 'selected' : ''}>${sprite.name}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="animation-speed">动画速度</label>
                        <input type="number" id="animation-speed" name="speed" min="0.01" step="0.01" value="${animation ? animation.speed : 0.1}" required>
                        <small>值越小，动画越慢</small>
                    </div>

                    <div class="form-group">
                        <label for="animation-loop">循环播放</label>
                        <select id="animation-loop" name="loop">
                            <option value="true" ${animation && animation.loop ? 'selected' : ''}>是</option>
                            <option value="false" ${animation && !animation.loop ? 'selected' : ''}>否</option>
                        </select>
                    </div>

                    <h4>方向帧设置</h4>

                    <div class="form-group frame-range-container">
                        <label>帧序列范围自动生成</label>
                        <div class="frame-range-controls">
                            <div class="direction-label">向下：</div>
                            <input type="number" id="down-start-frame" min="0" placeholder="起始帧" class="frame-input">
                            <span>到</span>
                            <input type="number" id="down-end-frame" min="0" placeholder="结束帧" class="frame-input">
                            <button type="button" class="btn btn-small frame-generate-btn" data-direction="down">生成</button>
                        </div>
                        <div class="frame-range-controls">
                            <div class="direction-label">向上：</div>
                            <input type="number" id="up-start-frame" min="0" placeholder="起始帧" class="frame-input">
                            <span>到</span>
                            <input type="number" id="up-end-frame" min="0" placeholder="结束帧" class="frame-input">
                            <button type="button" class="btn btn-small frame-generate-btn" data-direction="up">生成</button>
                        </div>
                        <div class="frame-range-controls">
                            <div class="direction-label">向左：</div>
                            <input type="number" id="left-start-frame" min="0" placeholder="起始帧" class="frame-input">
                            <span>到</span>
                            <input type="number" id="left-end-frame" min="0" placeholder="结束帧" class="frame-input">
                            <button type="button" class="btn btn-small frame-generate-btn" data-direction="left">生成</button>
                        </div>
                        <div class="frame-range-controls">
                            <div class="direction-label">向右：</div>
                            <input type="number" id="right-start-frame" min="0" placeholder="起始帧" class="frame-input">
                            <span>到</span>
                            <input type="number" id="right-end-frame" min="0" placeholder="结束帧" class="frame-input">
                            <button type="button" class="btn btn-small frame-generate-btn" data-direction="right">生成</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="animation-frames-down">向下帧序列</label>
                        <input type="text" id="animation-frames-down" name="frames.down" placeholder="例如: 0,1,2,3" value="${animation && animation.frames && animation.frames.down ? animation.frames.down.join(',') : ''}" required>
                    </div>

                    <div class="form-group">
                        <label for="animation-frames-up">向上帧序列</label>
                        <input type="text" id="animation-frames-up" name="frames.up" placeholder="例如: 4,5,6,7" value="${animation && animation.frames && animation.frames.up ? animation.frames.up.join(',') : ''}">
                    </div>

                    <div class="form-group">
                        <label for="animation-frames-left">向左帧序列</label>
                        <input type="text" id="animation-frames-left" name="frames.left" placeholder="例如: 8,9,10,11" value="${animation && animation.frames && animation.frames.left ? animation.frames.left.join(',') : ''}">
                    </div>

                    <div class="form-group">
                        <label for="animation-frames-right">向右帧序列</label>
                        <input type="text" id="animation-frames-right" name="frames.right" placeholder="例如: 12,13,14,15" value="${animation && animation.frames && animation.frames.right ? animation.frames.right.join(',') : ''}">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">${animation ? '更新' : '创建'}</button>
                        <button type="button" class="btn btn-secondary" id="cancel-animation-form">取消</button>
                    </div>
                </form>
            `;

            // 显示模态框
            showModal(animation ? '编辑动画' : '创建新动画', formHtml);

            // 添加表单提交事件
            document.getElementById('animation-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                await saveAnimationData(this);
            });

            // 添加帧范围生成功能
            document.querySelectorAll('.frame-generate-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const direction = this.dataset.direction;
                    const startFrame = parseInt(document.getElementById(`${direction}-start-frame`).value);
                    const endFrame = parseInt(document.getElementById(`${direction}-end-frame`).value);

                    if (isNaN(startFrame) || isNaN(endFrame)) {
                        showNotification('请输入有效的起始帧和结束帧', 'error');
                        return;
                    }

                    if (startFrame > endFrame) {
                        showNotification('起始帧不能大于结束帧', 'error');
                        return;
                    }

                    // 生成帧序列
                    const frames = [];
                    for (let i = startFrame; i <= endFrame; i++) {
                        frames.push(i);
                    }

                    // 更新帧序列输入框
                    document.getElementById(`animation-frames-${direction}`).value = frames.join(',');
                });
            });

            // 添加取消按钮事件
            document.getElementById('cancel-animation-form').addEventListener('click', closeModal);

        } catch (error) {
            console.error('显示动画表单失败:', error);
            showNotification('显示动画表单失败', 'error');
        }
    }

    /**
     * 保存动画数据
     * @param {HTMLFormElement} form 表单元素
     */
    async function saveAnimationData(form) {
        try {
            const formData = new FormData(form);
            const animationData = {
                name: formData.get('name'),
                type: formData.get('type'),
                spriteId: parseInt(formData.get('spriteId')),
                speed: parseFloat(formData.get('speed')),
                loop: formData.get('loop') === 'true',
                frames: {}
            };

            // 处理帧数据
            const directions = ['down', 'up', 'left', 'right'];
            directions.forEach(direction => {
                const framesStr = formData.get(`frames.${direction}`);
                if (framesStr) {
                    // 将逗号分隔的字符串转换为数字数组
                    animationData.frames[direction] = framesStr.split(',')
                        .map(s => parseInt(s.trim()))
                        .filter(n => !isNaN(n));
                }
            });

            // 至少需要一个方向的帧
            if (!animationData.frames.down || animationData.frames.down.length === 0) {
                throw new Error('至少需要设置向下方向的帧序列');
            }

            // 检查是新建还是更新
            const animationId = formData.get('id');

            if (animationId) {
                // 更新
                animationData.id = parseInt(animationId);
                await spriteManager.updateAnimation(animationData);
                showNotification('动画更新成功', 'success');
            } else {
                // 新建
                await spriteManager.createAnimation(animationData);
                showNotification('动画创建成功', 'success');
            }

            // 关闭模态框并刷新数据
            closeModal();
            loadAnimations();
            updateDashboardStats();

        } catch (error) {
            console.error('保存动画数据失败:', error);
            showNotification('保存动画数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示动画预览
     * @param {number} animationId 动画ID
     */
    async function showAnimationPreview(animationId) {
        try {
            const animation = await dbManager.getData(dbManager.stores.animations, animationId);
            const sprite = await dbManager.getData(dbManager.stores.sprites, animation.spriteId);

            // 显示预览容器
            const previewContainer = document.getElementById('animation-preview-container');
            previewContainer.classList.add('active');

            // 创建方向选择按钮
            const directions = Object.keys(animation.frames);
            const directionsHtml = directions.map(dir =>
                `<button class="btn btn-small direction-btn" data-direction="${dir}">${getDirectionName(dir)}</button>`
            ).join('');

            // 添加方向选择按钮到控制栏
            const directionsContainer = document.createElement('div');
            directionsContainer.className = 'direction-buttons';
            directionsContainer.innerHTML = directionsHtml;
            document.querySelector('.preview-controls').appendChild(directionsContainer);

            // 创建PIXI应用
            const canvasContainer = document.getElementById('animation-canvas-container');
            canvasContainer.innerHTML = '';

            const app = new PIXI.Application({
                width: 200,
                height: 200,
                backgroundColor: 0x333333,
                antialias: true
            });

            canvasContainer.appendChild(app.view);

            // 默认方向
            const defaultDirection = directions[0] || 'down';

            // 创建动画精灵
            const animatedSprite = await spriteManager.createAnimatedSprite(animationId, defaultDirection);
            animatedSprite.x = app.screen.width / 2;
            animatedSprite.y = app.screen.height / 2;
            animatedSprite.anchor.set(0.5);

            app.stage.addChild(animatedSprite);

            // 存储到全局变量，以便控制
            window.previewApp = app;
            window.previewSprite = animatedSprite;
            window.currentAnimationId = animationId;

            // 添加方向按钮点击事件
            document.querySelectorAll('.direction-btn').forEach(btn => {
                btn.addEventListener('click', async function() {
                    const direction = this.dataset.direction;
                    changeAnimationDirection(animationId, direction);
                });
            });

        } catch (error) {
            console.error('显示动画预览失败:', error);
            showNotification('显示动画预览失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取方向的中文名称
     * @param {string} direction 方向
     * @returns {string} 方向的中文名称
     */
    function getDirectionName(direction) {
        const directionNames = {
            'down': '向下',
            'up': '向上',
            'left': '向左',
            'right': '向右'
        };
        return directionNames[direction] || direction;
    }

    /**
     * 改变动画方向
     * @param {number} animationId 动画ID
     * @param {string} direction 方向
     */
    async function changeAnimationDirection(animationId, direction) {
        try {
            if (!window.previewApp || !window.previewSprite) return;

            // 保存之前的播放状态
            const wasPlaying = window.previewSprite.playing;

            // 移除旧精灵
            window.previewApp.stage.removeChild(window.previewSprite);
            window.previewSprite.destroy();

            // 创建新的方向精灵
            const animatedSprite = await spriteManager.createAnimatedSprite(animationId, direction);
            animatedSprite.x = window.previewApp.screen.width / 2;
            animatedSprite.y = window.previewApp.screen.height / 2;
            animatedSprite.anchor.set(0.5);

            // 如果之前在播放则继续播放
            if (wasPlaying) {
                animatedSprite.play();
            }

            window.previewApp.stage.addChild(animatedSprite);
            window.previewSprite = animatedSprite;

            // 高亮当前选中的方向按钮
            document.querySelectorAll('.direction-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.direction === direction);
            });

        } catch (error) {
            console.error('改变动画方向失败:', error);
            showNotification('改变动画方向失败: ' + error.message, 'error');
        }
    }

    /**
     * 播放动画预览
     */
    function playAnimationPreview() {
        if (window.previewSprite) {
            window.previewSprite.play();
        }
    }

    /**
     * 停止动画预览
     */
    function stopAnimationPreview() {
        if (window.previewSprite) {
            window.previewSprite.stop();
        }
    }

    /**
     * 关闭动画预览
     */
    function closeAnimationPreview() {
        const previewContainer = document.getElementById('animation-preview-container');
        previewContainer.classList.remove('active');

        // 移除方向按钮
        const directionsContainer = document.querySelector('.direction-buttons');
        if (directionsContainer) {
            directionsContainer.remove();
        }

        // 清理资源
        if (window.previewApp) {
            window.previewApp.destroy(true);
            window.previewApp = null;
            window.previewSprite = null;
            window.currentAnimationId = null;
        }
    }

    /**
     * 确认删除
     * @param {string} type 实体类型
     * @param {number} id 实体ID
     */
    function confirmDelete(type, id) {
        const entityTypes = {
            player: '玩家',
            enemy: '敌人',
            sprite: '精灵表',
            animation: '动画'
        };

        const entityName = entityTypes[type] || '项目';

        const confirmHtml = `
            <div class="confirm-dialog">
                <p>确定要删除这个${entityName}吗？此操作不可撤销。</p>
                <div class="form-actions">
                    <button id="confirm-delete-btn" class="btn btn-danger">删除</button>
                    <button id="cancel-delete-btn" class="btn btn-secondary">取消</button>
                </div>
            </div>
        `;

        showModal(`删除${entityName}`, confirmHtml);

        document.getElementById('confirm-delete-btn').addEventListener('click', async () => {
            await deleteEntity(type, id);
        });

        document.getElementById('cancel-delete-btn').addEventListener('click', closeModal);
    }

    /**
     * 删除实体
     * @param {string} type 实体类型
     * @param {number} id 实体ID
     */
    async function deleteEntity(type, id) {
        try {
            switch (type) {
                case 'player':
                    await entityManager.deletePlayer(id);
                    loadPlayers();
                    break;
                case 'enemy':
                    await entityManager.deleteEnemy(id);
                    loadEnemies();
                    break;
                case 'sprite':
                    await spriteManager.deleteSprite(id);
                    loadSprites();
                    break;
                case 'animation':
                    await spriteManager.deleteAnimation(id);
                    loadAnimations();
                    break;
                default:
                    throw new Error('未知的实体类型');
            }

            closeModal();
            updateDashboardStats();
            showNotification('删除成功', 'success');

        } catch (error) {
            console.error('删除失败:', error);
            showNotification('删除失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示模态框
     * @param {string} title 标题
     * @param {string} content 内容HTML
     */
    function showModal(title, content) {
        const modalContainer = document.getElementById('modal-container');
        const modalTitle = document.getElementById('modal-title');
        const modalContent = document.getElementById('modal-content');

        if (!modalContainer || !modalTitle || !modalContent) {
            console.error('找不到模态框元素');
            return;
        }

        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        modalContainer.classList.add('active');
    }

    /**
     * 关闭模态框
     */
    function closeModal() {
        const modalContainer = document.getElementById('modal-container');
        if (modalContainer) {
            modalContainer.classList.remove('active');
        } else {
            console.warn('找不到模态框容器元素');
        }
    }

    /**
     * 将文件转换为Base64字符串
     * @param {File} file 文件对象
     * @returns {Promise<string>} Base64字符串
     */
    function convertFileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = () => {
                resolve(reader.result);
            };

            reader.onerror = (error) => {
                reject(error);
            };

            reader.readAsDataURL(file);
        });
    }

    /**
     * 显示通知
     * @param {string} message 消息
     * @param {string} type 类型 ('success', 'error', 'info')
     */
    function showNotification(message, type = 'info') {
        try {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;

            // 添加到页面
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // 自动关闭
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    try {
                        // 检查元素是否仍在文档中
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    } catch (error) {
                        console.warn('移除通知元素失败:', error);
                    }
                }, 300);
            }, 3000);

            // 同时在控制台输出通知
            const consoleMethod = type === 'error' ? console.error :
                                 type === 'success' ? console.log : console.info;
            consoleMethod(`通知 [${type}]: ${message}`);
        } catch (error) {
            // 如果显示通知失败，至少在控制台输出
            console.error('显示通知失败:', error);
            console.log(`通知内容 [${type}]: ${message}`);
        }
    }

    /**
     * 显示实体范围预览
     * @param {string} entityType 实体类型 ('player' 或 'enemy')
     */
    function showEntityRangePreview(entityType) {
        try {
            // 获取表单数据
            const form = document.getElementById(`${entityType}-form`);
            if (!form) {
                console.error('找不到表单');
                return;
            }

            const formData = new FormData(form);

            // 获取实体数据
            const entityData = {
                name: formData.get('name') || `${entityType === 'player' ? '玩家' : '敌人'}`,
                defaultWidth: parseInt(formData.get('defaultAnimationWidth')) || (entityType === 'player' ? 60 : 50),
                defaultHeight: parseInt(formData.get('defaultAnimationHeight')) || (entityType === 'player' ? 60 : 50),
                collisionBox: {
                    width: parseInt(formData.get('collisionBox.width')) || (entityType === 'player' ? 40 : 30),
                    height: parseInt(formData.get('collisionBox.height')) || (entityType === 'player' ? 40 : 30)
                },
                attackRange: {
                    width: parseInt(formData.get('attackRange.width')) || (entityType === 'player' ? 80 : 60),
                    height: parseInt(formData.get('attackRange.height')) || (entityType === 'player' ? 80 : 60)
                }
            };

            // 如果是敌人，还要获取警戒范围
            if (entityType === 'enemy') {
                entityData.alertRange = {
                    width: parseInt(formData.get('alertRange.width')) || 120,
                    height: parseInt(formData.get('alertRange.height')) || 120
                };
            }

            // 创建预览窗口
            createRangePreviewWindow(entityType, entityData);

        } catch (error) {
            console.error('显示范围预览失败:', error);
            showNotification('显示范围预览失败', 'error');
        }
    }

    /**
     * 创建范围预览窗口
     * @param {string} entityType 实体类型
     * @param {Object} entityData 实体数据
     */
    async function createRangePreviewWindow(entityType, entityData) {
        // 移除现有的预览窗口
        const existingPreview = document.getElementById('range-preview-container');
        if (existingPreview) {
            existingPreview.remove();
        }

        // 创建预览容器
        const previewContainer = document.createElement('div');
        previewContainer.id = 'range-preview-container';
        previewContainer.className = 'range-preview-container';

        // 创建预览内容
        previewContainer.innerHTML = `
            <div class="preview-header">
                <h4>${entityData.name} - 范围预览</h4>
                <button id="close-range-preview" class="btn btn-small btn-danger">关闭</button>
            </div>
            <div class="preview-canvas-container">
                <div id="range-preview-pixi-container"></div>
            </div>
            <div class="preview-legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: transparent; border: 2px solid #e74c3c;"></div>
                    <span>碰撞框 (半径: ${Math.max(entityData.collisionBox.width, entityData.collisionBox.height)/2})</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: transparent; border: 2px dashed #f39c12;"></div>
                    <span>攻击范围 (半径: ${Math.max(entityData.attackRange.width, entityData.attackRange.height)/2})</span>
                </div>
                ${entityType === 'enemy' ? `
                <div class="legend-item">
                    <div class="legend-color" style="background-color: transparent; border: 2px dashed #9b59b6;"></div>
                    <span>警戒范围 (半径: ${Math.max(entityData.alertRange.width, entityData.alertRange.height)/2})</span>
                </div>
                ` : ''}
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #3498db;"></div>
                    <span>实体动画 (${entityData.defaultWidth}x${entityData.defaultHeight})</span>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(previewContainer);

        // 创建PIXI预览
        await createPixiRangePreview(entityType, entityData);

        // 添加关闭事件
        document.getElementById('close-range-preview').addEventListener('click', function() {
            // 清理PIXI应用
            if (window.rangePreviewApp) {
                window.rangePreviewApp.destroy(true);
                window.rangePreviewApp = null;
            }
            previewContainer.remove();
        });
    }

    /**
     * 创建PIXI范围预览
     * @param {string} entityType 实体类型
     * @param {Object} entityData 实体数据
     */
    async function createPixiRangePreview(entityType, entityData) {
        try {
            const container = document.getElementById('range-preview-pixi-container');
            if (!container) {
                console.error('找不到预览容器');
                return;
            }

            // 检查PIXI是否可用
            if (typeof PIXI === 'undefined') {
                console.error('PIXI.js未加载');
                createFallbackPreview(entityType, entityData);
                return;
            }

            // 清理旧的应用
            if (window.rangePreviewApp) {
                try {
                    window.rangePreviewApp.destroy(true);
                } catch (e) {
                    console.warn('清理旧应用时出错:', e);
                }
                window.rangePreviewApp = null;
            }

            // 清空容器
            container.innerHTML = '';

            // 创建PIXI应用
            console.log('开始创建PIXI应用...');
            window.rangePreviewApp = new PIXI.Application({
                width: 400,
                height: 300,
                backgroundColor: 0x333333,
                antialias: true,
                forceCanvas: false // 尝试使用WebGL，失败时自动回退到Canvas
            });

            console.log('PIXI应用创建成功');
            container.appendChild(window.rangePreviewApp.view);

            const centerX = window.rangePreviewApp.screen.width / 2;
            const centerY = window.rangePreviewApp.screen.height / 2;

            // 计算缩放比例
            const maxRange = entityType === 'enemy' ?
                Math.max(entityData.alertRange.width, entityData.alertRange.height) :
                Math.max(entityData.attackRange.width, entityData.attackRange.height);
            const scale = Math.min(window.rangePreviewApp.screen.width * 0.8, window.rangePreviewApp.screen.height * 0.8) / maxRange;

            // 先加载并显示待机动画（在底层）
            await loadEntityIdleAnimation(entityType, entityData, centerX, centerY, scale);

            // 然后绘制范围（在上层，这样可以清楚看到范围边界）

            // 绘制警戒范围（仅敌人）
            if (entityType === 'enemy') {
                console.log('绘制敌人警戒范围...');
                const alertRadius = Math.max(entityData.alertRange.width, entityData.alertRange.height) / 2 * scale;
                const alertCircle = new PIXI.Graphics();
                alertCircle.lineStyle(2, 0x9b59b6, 1);
                // 注意：PIXI.Graphics没有setLineDash方法，我们需要用其他方式创建虚线
                alertCircle.drawCircle(0, 0, alertRadius);
                alertCircle.x = centerX;
                alertCircle.y = centerY;
                window.rangePreviewApp.stage.addChild(alertCircle);
            }

            // 绘制攻击范围
            console.log('绘制攻击范围...');
            const attackRadius = Math.max(entityData.attackRange.width, entityData.attackRange.height) / 2 * scale;
            const attackCircle = new PIXI.Graphics();
            attackCircle.lineStyle(2, 0xf39c12, 1);
            attackCircle.drawCircle(0, 0, attackRadius);
            attackCircle.x = centerX;
            attackCircle.y = centerY;
            window.rangePreviewApp.stage.addChild(attackCircle);

            // 绘制碰撞框
            console.log('绘制碰撞框...');
            const collisionRadius = Math.max(entityData.collisionBox.width, entityData.collisionBox.height) / 2 * scale;
            const collisionCircle = new PIXI.Graphics();
            collisionCircle.lineStyle(2, 0xe74c3c, 1);
            collisionCircle.drawCircle(0, 0, collisionRadius);
            collisionCircle.x = centerX;
            collisionCircle.y = centerY;
            window.rangePreviewApp.stage.addChild(collisionCircle);

            // 最后绘制中心点（在最上层）
            const centerPoint = new PIXI.Graphics();
            centerPoint.beginFill(0x2c3e50);
            centerPoint.drawCircle(0, 0, 3);
            centerPoint.x = centerX;
            centerPoint.y = centerY;
            window.rangePreviewApp.stage.addChild(centerPoint);

        } catch (error) {
            console.error('创建PIXI范围预览失败:', error);
            // 如果PIXI预览失败，回退到简单的图形显示
            createFallbackPreview(entityType, entityData);
        }
    }

    /**
     * 加载实体待机动画
     * @param {string} entityType 实体类型
     * @param {Object} entityData 实体数据
     * @param {number} centerX 中心X坐标
     * @param {number} centerY 中心Y坐标
     * @param {number} scale 缩放比例
     */
    async function loadEntityIdleAnimation(entityType, entityData, centerX, centerY, scale) {
        try {
            // 获取表单中的待机动画ID
            const form = document.getElementById(`${entityType}-form`);
            if (!form) return;

            const formData = new FormData(form);
            const idleAnimationId = formData.get('animations.idle');

            if (idleAnimationId && spriteManager) {
                // 尝试创建待机动画
                const animatedSprite = await spriteManager.createAnimatedSprite(parseInt(idleAnimationId), 'down');

                // 设置动画尺寸
                animatedSprite.width = entityData.defaultWidth * scale;
                animatedSprite.height = entityData.defaultHeight * scale;

                // 设置位置和锚点
                animatedSprite.x = centerX;
                animatedSprite.y = centerY;
                animatedSprite.anchor.set(0.5);

                // 播放动画
                animatedSprite.play();

                window.rangePreviewApp.stage.addChild(animatedSprite);

                console.log('成功加载待机动画');
            } else {
                // 没有动画，创建简单的矩形表示实体
                createSimpleEntitySprite(entityData, centerX, centerY, scale);
            }
        } catch (error) {
            console.warn('加载待机动画失败，使用简单图形:', error);
            // 创建简单的矩形表示实体
            createSimpleEntitySprite(entityData, centerX, centerY, scale);
        }
    }

    /**
     * 创建简单的实体精灵
     * @param {Object} entityData 实体数据
     * @param {number} centerX 中心X坐标
     * @param {number} centerY 中心Y坐标
     * @param {number} scale 缩放比例
     */
    function createSimpleEntitySprite(entityData, centerX, centerY, scale) {
        const entitySprite = new PIXI.Graphics();
        entitySprite.beginFill(0x3498db);
        const width = entityData.defaultWidth * scale;
        const height = entityData.defaultHeight * scale;
        entitySprite.drawRect(-width/2, -height/2, width, height);
        entitySprite.x = centerX;
        entitySprite.y = centerY;
        window.rangePreviewApp.stage.addChild(entitySprite);
    }

    /**
     * 创建回退预览（当PIXI失败时）
     * @param {string} entityType 实体类型
     * @param {Object} entityData 实体数据
     */
    function createFallbackPreview(entityType, entityData) {
        const container = document.getElementById('range-preview-pixi-container');
        if (!container) return;

        console.log('使用Canvas回退预览...');

        // 创建Canvas元素
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        canvas.style.border = '1px solid #ddd';
        canvas.style.borderRadius = '4px';
        canvas.style.backgroundColor = '#333';

        container.innerHTML = '';
        container.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        // 计算缩放比例
        const maxRange = entityType === 'enemy' ?
            Math.max(entityData.alertRange.width, entityData.alertRange.height) :
            Math.max(entityData.attackRange.width, entityData.attackRange.height);
        const scale = Math.min(canvas.width * 0.8, canvas.height * 0.8) / maxRange;

        // 先绘制实体（在底层）
        ctx.fillStyle = '#3498db';
        const entityWidth = entityData.defaultWidth * scale;
        const entityHeight = entityData.defaultHeight * scale;
        ctx.fillRect(centerX - entityWidth/2, centerY - entityHeight/2, entityWidth, entityHeight);

        // 然后绘制范围（在上层）

        // 绘制警戒范围（仅敌人）
        if (entityType === 'enemy') {
            const alertRadius = Math.max(entityData.alertRange.width, entityData.alertRange.height) / 2 * scale;
            ctx.strokeStyle = '#9b59b6';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.arc(centerX, centerY, alertRadius, 0, 2 * Math.PI);
            ctx.stroke();
        }

        // 绘制攻击范围
        const attackRadius = Math.max(entityData.attackRange.width, entityData.attackRange.height) / 2 * scale;
        ctx.strokeStyle = '#f39c12';
        ctx.lineWidth = 2;
        ctx.setLineDash([3, 3]);
        ctx.beginPath();
        ctx.arc(centerX, centerY, attackRadius, 0, 2 * Math.PI);
        ctx.stroke();

        // 绘制碰撞框
        const collisionRadius = Math.max(entityData.collisionBox.width, entityData.collisionBox.height) / 2 * scale;
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.beginPath();
        ctx.arc(centerX, centerY, collisionRadius, 0, 2 * Math.PI);
        ctx.stroke();

        // 最后绘制中心点（在最上层）
        ctx.fillStyle = '#2c3e50';
        ctx.beginPath();
        ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI);
        ctx.fill();
    }

    /**
     * 设置实时预览功能
     * @param {string} entityType 实体类型
     */
    function setupRealTimePreview(entityType) {
        // 获取所有相关的输入框
        const inputs = [
            `${entityType}-default-width`,
            `${entityType}-default-height`,
            `${entityType}-collision-width`,
            `${entityType}-collision-height`,
            `${entityType}-attack-range-width`,
            `${entityType}-attack-range-height`
        ];

        // 如果是敌人，添加警戒范围输入框
        if (entityType === 'enemy') {
            inputs.push(
                `${entityType}-alert-range-width`,
                `${entityType}-alert-range-height`
            );
        }

        // 为每个输入框添加事件监听器
        inputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', function() {
                    // 检查是否有预览窗口打开
                    const previewContainer = document.getElementById('range-preview-container');
                    if (previewContainer) {
                        // 更新预览
                        updateRangePreview(entityType);
                    }
                });
            }
        });
    }

    /**
     * 更新范围预览
     * @param {string} entityType 实体类型
     */
    async function updateRangePreview(entityType) {
        try {
            // 获取表单数据
            const form = document.getElementById(`${entityType}-form`);
            if (!form) return;

            const formData = new FormData(form);

            // 获取实体数据
            const entityData = {
                name: formData.get('name') || `${entityType === 'player' ? '玩家' : '敌人'}`,
                defaultWidth: parseInt(formData.get('defaultAnimationWidth')) || (entityType === 'player' ? 60 : 50),
                defaultHeight: parseInt(formData.get('defaultAnimationHeight')) || (entityType === 'player' ? 60 : 50),
                collisionBox: {
                    width: parseInt(formData.get('collisionBox.width')) || (entityType === 'player' ? 40 : 30),
                    height: parseInt(formData.get('collisionBox.height')) || (entityType === 'player' ? 40 : 30)
                },
                attackRange: {
                    width: parseInt(formData.get('attackRange.width')) || (entityType === 'player' ? 80 : 60),
                    height: parseInt(formData.get('attackRange.height')) || (entityType === 'player' ? 80 : 60)
                }
            };

            // 如果是敌人，还要获取警戒范围
            if (entityType === 'enemy') {
                entityData.alertRange = {
                    width: parseInt(formData.get('alertRange.width')) || 120,
                    height: parseInt(formData.get('alertRange.height')) || 120
                };
            }

            // 更新图例
            updatePreviewLegend(entityType, entityData);

            // 重新创建PIXI预览
            await createPixiRangePreview(entityType, entityData);

        } catch (error) {
            console.error('更新范围预览失败:', error);
        }
    }

    /**
     * 更新预览图例
     * @param {string} entityType 实体类型
     * @param {Object} entityData 实体数据
     */
    function updatePreviewLegend(entityType, entityData) {
        const legendItems = document.querySelectorAll('.legend-item span');
        if (legendItems.length >= 3) {
            legendItems[0].textContent = `碰撞框 (半径: ${Math.max(entityData.collisionBox.width, entityData.collisionBox.height)/2})`;
            legendItems[1].textContent = `攻击范围 (半径: ${Math.max(entityData.attackRange.width, entityData.attackRange.height)/2})`;

            if (entityType === 'enemy' && legendItems[2]) {
                legendItems[2].textContent = `警戒范围 (半径: ${Math.max(entityData.alertRange.width, entityData.alertRange.height)/2})`;
                if (legendItems[3]) {
                    legendItems[3].textContent = `实体动画 (${entityData.defaultWidth}x${entityData.defaultHeight})`;
                }
            } else {
                if (legendItems[2]) {
                    legendItems[2].textContent = `实体动画 (${entityData.defaultWidth}x${entityData.defaultHeight})`;
                }
            }
        }
    }
});
