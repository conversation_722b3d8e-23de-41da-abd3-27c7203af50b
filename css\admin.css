/* 管理后台样式 */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --danger-color: #e74c3c;
    --danger-dark: #c0392b;
    --text-color: #333;
    --light-text: #ecf0f1;
    --border-color: #ddd;
    --bg-color: #f5f5f5;
    --card-bg: #fff;
    --header-bg: #34495e;
    --sidebar-bg: #2c3e50;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
}

/* 布局 */
.admin-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.admin-header {
    background-color: var(--header-bg);
    color: var(--light-text);
    padding: 1rem 2rem;
}

.admin-header h1 {
    margin-bottom: 1rem;
}

.admin-nav ul {
    display: flex;
    list-style: none;
    gap: 1rem;
}

.admin-nav a {
    color: var(--light-text);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.admin-nav a:hover, .admin-nav a.active {
    background-color: var(--primary-color);
}

.admin-content {
    flex: 1;
    padding: 2rem;
}

/* 部分 */
.admin-section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.admin-section.active {
    display: block;
}

.admin-section h2 {
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

/* 仪表盘 */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.quick-actions {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

/* 表格 */
.data-table-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th, .data-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: #f9f9f9;
    font-weight: bold;
}

.data-table tr:hover {
    background-color: #f5f5f5;
}

/* 精灵表网格 */
.sprites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.sprite-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.sprite-preview {
    width: 100%;
    height: 150px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 1rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sprite-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.sprite-info {
    flex: 1;
}

.sprite-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
}

/* 表单 */
.settings-form {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    max-width: 600px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
}

/* 按钮 */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.1s;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: var(--danger-dark);
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
}

.section-actions {
    margin-bottom: 1.5rem;
}

/* 模态框 */
.modal-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-container.active {
    display: flex;
}

.modal {
    background-color: var(--card-bg);
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-content {
    padding: 1.5rem;
}

/* 动画预览 */
.animation-preview-container {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    height: 300px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 900;
    overflow: hidden;
    flex-direction: column;
}

.animation-preview-container.active {
    display: flex;
}

.preview-controls {
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    background-color: #f5f5f5;
}

.preview-controls > div:first-child {
    display: flex;
    justify-content: space-between;
}

.direction-buttons {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.direction-btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.85rem;
    background-color: #e0e0e0;
    color: #333;
}

.direction-btn.active {
    background-color: var(--primary-color);
    color: white;
}

#animation-canvas-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #333;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 通知 */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.notification {
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    color: white;
    font-weight: bold;
}

.notification-success {
    background-color: var(--secondary-color);
}

.notification-error {
    background-color: var(--danger-color);
}

.notification-info {
    background-color: var(--primary-color);
}

/* 确认对话框 */
.confirm-dialog {
    text-align: center;
    padding: 1rem 0;
}

.confirm-dialog p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

/* 碰撞和范围设置 */
.collision-range-settings {
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.collision-range-settings .form-group {
    margin-bottom: 1rem;
}

/* 范围预览容器 */
.range-preview-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 1100;
    overflow: hidden;
}

.preview-header {
    padding: 1rem 1.5rem;
    background-color: var(--header-bg);
    color: var(--light-text);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h4 {
    margin: 0;
    font-size: 1.1rem;
}

.preview-canvas-container {
    padding: 1rem;
    text-align: center;
    background-color: #f8f9fa;
}

#range-preview-canvas {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: white;
}

#range-preview-pixi-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

#range-preview-pixi-container canvas {
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.preview-legend {
    padding: 1rem 1.5rem;
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    margin-right: 0.5rem;
    border: 1px solid #ccc;
}

/* 地图编辑器 */
.maps-container {
    display: flex;
    gap: 1.5rem;
    margin-top: 1.5rem;
    height: calc(100vh - 200px);
}

.maps-list {
    width: 250px;
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.map-item {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s;
}

.map-item:hover {
    background-color: #f5f5f5;
}

.map-item.active {
    background-color: #e3f2fd;
    border-left: 3px solid var(--primary-color);
}

.map-editor-container {
    flex: 1;
    display: flex;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.map-editor-tools {
    width: 250px;
    padding: 1rem;
    background-color: #f5f5f5;
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}

.tool-group {
    margin-bottom: 1.5rem;
}

.tool-group h4 {
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid var(--border-color);
}

.tool-btn {
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
    text-align: left;
}

.tool-btn.active {
    background-color: var(--primary-dark);
}

.map-editor-canvas-container {
    flex: 1;
    overflow: auto;
    background-color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
}

#map-editor-canvas {
    position: relative;
    background-color: transparent;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.map-grid-cell {
    position: absolute;
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    background-color: transparent;
    z-index: 1;
}

.map-grid-cell:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.map-grid-cell.wall {
    background-color: rgba(128, 128, 128, 0.8);
}

.map-grid-cell.player-start {
    background-color: rgba(46, 204, 113, 0.8);
}

.map-grid-cell.enemy {
    background-color: rgba(231, 76, 60, 0.8);
}

.map-grid-cell.path {
    background-color: rgba(52, 152, 219, 0.5);
}

.map-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    object-fit: cover;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-nav ul {
        flex-wrap: wrap;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .sprites-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .animation-preview-container {
        width: 250px;
        height: 250px;
    }

    .maps-container {
        flex-direction: column;
        height: auto;
    }

    .maps-list {
        width: 100%;
        max-height: 200px;
    }

    .map-editor-container {
        flex-direction: column;
        height: 600px;
    }

    .map-editor-tools {
        width: 100%;
        max-height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
}

/* 动画帧序列范围选取 */
.frame-range-container {
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.frame-range-controls {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.frame-range-controls:last-child {
    margin-bottom: 0;
}

.direction-label {
    width: 50px;
    font-weight: bold;
}

.frame-input {
    width: 80px;
    padding: 5px;
    margin: 0 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.frame-generate-btn {
    margin-left: 10px;
    background-color: var(--secondary-color);
}

.frame-generate-btn:hover {
    background-color: var(--secondary-dark);
}

/* 动画设置容器 */
.animation-settings-container {
    margin-bottom: 20px;
}

.animation-settings-item {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f9f9f9;
}

.animation-size-settings {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.animation-size-settings .form-group {
    flex: 1;
}

/* 动画设置标题 */
.animation-settings-item h5 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .animation-size-settings {
        flex-direction: column;
        gap: 5px;
    }
}
