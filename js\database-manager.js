/**
 * 游戏数据库管理类
 * 使用IndexedDB存储和管理游戏数据
 */
class DatabaseManager {
    constructor() {
        this.dbName = 'AdventureGameDB';
        this.dbVersion = 2; // 更新数据库版本以添加地图存储
        this.db = null;

        // 存储对象
        this.stores = {
            players: 'players',
            enemies: 'enemies',
            sprites: 'sprites',
            animations: 'animations',
            gameSettings: 'gameSettings',
            gameSaves: 'gameSaves',
            maps: 'maps' // 添加地图存储
        };

        // 初始化数据库
        this.initDatabase();
    }

    /**
     * 初始化数据库
     * @returns {Promise} 数据库初始化Promise
     */
    async initDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            // 数据库打开错误
            request.onerror = (event) => {
                console.error('数据库打开失败:', event.target.error);
                reject(event.target.error);
            };

            // 数据库打开成功
            request.onsuccess = (event) => {
                this.db = event.target.result;
                console.log('数据库连接成功');
                resolve(this.db);
            };

            // 数据库需要升级（首次创建或版本更新）
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                const oldVersion = event.oldVersion;

                console.log(`数据库升级: 从版本 ${oldVersion} 到版本 ${this.dbVersion}`);

                // 创建玩家存储
                if (!db.objectStoreNames.contains(this.stores.players)) {
                    const playerStore = db.createObjectStore(this.stores.players, { keyPath: 'id', autoIncrement: true });
                    playerStore.createIndex('name', 'name', { unique: false });
                    playerStore.createIndex('type', 'type', { unique: false });
                }

                // 创建敌人存储
                if (!db.objectStoreNames.contains(this.stores.enemies)) {
                    const enemyStore = db.createObjectStore(this.stores.enemies, { keyPath: 'id', autoIncrement: true });
                    enemyStore.createIndex('name', 'name', { unique: false });
                    enemyStore.createIndex('type', 'type', { unique: false });
                }

                // 创建精灵表存储
                if (!db.objectStoreNames.contains(this.stores.sprites)) {
                    const spriteStore = db.createObjectStore(this.stores.sprites, { keyPath: 'id', autoIncrement: true });
                    spriteStore.createIndex('name', 'name', { unique: false });
                    spriteStore.createIndex('type', 'type', { unique: false });
                }

                // 创建动画存储
                if (!db.objectStoreNames.contains(this.stores.animations)) {
                    const animationStore = db.createObjectStore(this.stores.animations, { keyPath: 'id', autoIncrement: true });
                    animationStore.createIndex('name', 'name', { unique: false });
                    animationStore.createIndex('spriteId', 'spriteId', { unique: false });
                }

                // 创建游戏设置存储
                if (!db.objectStoreNames.contains(this.stores.gameSettings)) {
                    db.createObjectStore(this.stores.gameSettings, { keyPath: 'id', autoIncrement: true });
                }

                // 创建游戏存档存储
                if (!db.objectStoreNames.contains(this.stores.gameSaves)) {
                    const saveStore = db.createObjectStore(this.stores.gameSaves, { keyPath: 'id', autoIncrement: true });
                    saveStore.createIndex('name', 'name', { unique: false });
                    saveStore.createIndex('date', 'date', { unique: false });
                }

                // 创建地图存储
                if (!db.objectStoreNames.contains(this.stores.maps)) {
                    const mapStore = db.createObjectStore(this.stores.maps, { keyPath: 'id', autoIncrement: true });
                    mapStore.createIndex('name', 'name', { unique: false });
                    mapStore.createIndex('date', 'date', { unique: false });
                    console.log('地图存储创建完成');
                }

                console.log('数据库结构创建完成');
            };
        });
    }

    /**
     * 通用添加数据方法
     * @param {string} storeName 存储名称
     * @param {Object} data 要添加的数据
     * @returns {Promise} 添加操作的Promise
     */
    async addData(storeName, data) {
        if (!this.db) await this.initDatabase();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);

            const request = store.add(data);

            request.onsuccess = (event) => {
                console.log(`添加数据到${storeName}成功, ID: ${event.target.result}`);
                resolve(event.target.result);
            };

            request.onerror = (event) => {
                console.error(`添加数据到${storeName}失败:`, event.target.error);
                reject(event.target.error);
            };
        });
    }

    /**
     * 通用获取数据方法
     * @param {string} storeName 存储名称
     * @param {number|string} id 数据ID
     * @returns {Promise} 获取操作的Promise
     */
    async getData(storeName, id) {
        if (!this.db) await this.initDatabase();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);

            const request = store.get(id);

            request.onsuccess = (event) => {
                if (event.target.result) {
                    resolve(event.target.result);
                } else {
                    reject(new Error(`未找到ID为${id}的数据`));
                }
            };

            request.onerror = (event) => {
                console.error(`获取数据失败:`, event.target.error);
                reject(event.target.error);
            };
        });
    }

    /**
     * 通用更新数据方法
     * @param {string} storeName 存储名称
     * @param {Object} data 要更新的数据（必须包含id）
     * @returns {Promise} 更新操作的Promise
     */
    async updateData(storeName, data) {
        if (!this.db) await this.initDatabase();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);

            const request = store.put(data);

            request.onsuccess = (event) => {
                console.log(`更新数据成功, ID: ${data.id}`);
                resolve(data);
            };

            request.onerror = (event) => {
                console.error(`更新数据失败:`, event.target.error);
                reject(event.target.error);
            };
        });
    }

    /**
     * 通用删除数据方法
     * @param {string} storeName 存储名称
     * @param {number|string} id 要删除的数据ID
     * @returns {Promise} 删除操作的Promise
     */
    async deleteData(storeName, id) {
        if (!this.db) await this.initDatabase();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);

            const request = store.delete(id);

            request.onsuccess = (event) => {
                console.log(`删除数据成功, ID: ${id}`);
                resolve(true);
            };

            request.onerror = (event) => {
                console.error(`删除数据失败:`, event.target.error);
                reject(event.target.error);
            };
        });
    }

    /**
     * 通用获取所有数据方法
     * @param {string} storeName 存储名称
     * @returns {Promise} 获取所有数据的Promise
     */
    async getAllData(storeName) {
        if (!this.db) await this.initDatabase();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);

            const request = store.getAll();

            request.onsuccess = (event) => {
                resolve(event.target.result);
            };

            request.onerror = (event) => {
                console.error(`获取所有数据失败:`, event.target.error);
                reject(event.target.error);
            };
        });
    }
}
