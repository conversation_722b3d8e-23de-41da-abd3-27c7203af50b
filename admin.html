<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏管理后台</title>
    <link rel="stylesheet" href="css/admin.css">
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@7.x/dist/pixi.min.js"></script>
</head>
<body>
    <div class="admin-container">
        <header class="admin-header">
            <h1>2D冒险游戏管理后台</h1>
            <nav class="admin-nav">
                <ul>
                    <li><a href="#" data-section="dashboard" class="active">仪表盘</a></li>
                    <li><a href="#" data-section="players">玩家管理</a></li>
                    <li><a href="#" data-section="enemies">敌人管理</a></li>
                    <li><a href="#" data-section="maps">地图管理</a></li>
                    <li><a href="#" data-section="sprites">精灵表管理</a></li>
                    <li><a href="#" data-section="animations">动画管理</a></li>
                    <li><a href="#" data-section="settings">游戏设置</a></li>
                    <li><a href="index.html" target="_blank">查看游戏</a></li>
                </ul>
            </nav>
        </header>

        <main class="admin-content">
            <!-- 仪表盘 -->
            <section id="dashboard" class="admin-section active">
                <h2>仪表盘</h2>
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <h3>玩家角色</h3>
                        <p class="stat-value" id="player-count">0</p>
                    </div>
                    <div class="stat-card">
                        <h3>敌人类型</h3>
                        <p class="stat-value" id="enemy-count">0</p>
                    </div>
                    <div class="stat-card">
                        <h3>地图</h3>
                        <p class="stat-value" id="map-count">0</p>
                    </div>
                    <div class="stat-card">
                        <h3>精灵表</h3>
                        <p class="stat-value" id="sprite-count">0</p>
                    </div>
                    <div class="stat-card">
                        <h3>动画</h3>
                        <p class="stat-value" id="animation-count">0</p>
                    </div>
                </div>
                <div class="quick-actions">
                    <h3>快速操作</h3>
                    <div class="action-buttons">
                        <button id="add-player-btn" class="btn btn-primary">添加玩家</button>
                        <button id="add-enemy-btn" class="btn btn-primary">添加敌人</button>
                        <button id="add-map-btn" class="btn btn-primary">添加地图</button>
                        <button id="add-sprite-btn" class="btn btn-primary">添加精灵表</button>
                        <button id="add-animation-btn" class="btn btn-primary">添加动画</button>
                    </div>
                </div>
            </section>

            <!-- 玩家管理 -->
            <section id="players" class="admin-section">
                <h2>玩家管理</h2>
                <div class="section-actions">
                    <button id="create-player-btn" class="btn btn-primary">创建新玩家</button>
                </div>
                <div class="data-table-container">
                    <table class="data-table" id="players-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>生命值</th>
                                <th>攻击力</th>
                                <th>防御力</th>
                                <th>速度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 玩家数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- 敌人管理 -->
            <section id="enemies" class="admin-section">
                <h2>敌人管理</h2>
                <div class="section-actions">
                    <button id="create-enemy-btn" class="btn btn-primary">创建新敌人</button>
                </div>
                <div class="data-table-container">
                    <table class="data-table" id="enemies-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>生命值</th>
                                <th>攻击力</th>
                                <th>防御力</th>
                                <th>速度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 敌人数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- 地图管理 -->
            <section id="maps" class="admin-section">
                <h2>地图管理</h2>
                <div class="section-actions">
                    <button id="create-map-btn" class="btn btn-primary">创建新地图</button>
                </div>
                <div class="maps-container">
                    <div class="maps-list" id="maps-list">
                        <!-- 地图列表将通过JavaScript动态加载 -->
                    </div>
                    <div class="map-editor-container" id="map-editor-container">
                        <div class="map-editor-tools">
                            <div class="tool-group">
                                <h4>地图工具</h4>
                                <button id="tool-add-wall" class="btn btn-small tool-btn">添加墙壁</button>
                                <button id="tool-remove-wall" class="btn btn-small tool-btn">删除墙壁</button>
                                <button id="tool-clear-walls" class="btn btn-small tool-btn">清除所有墙壁</button>
                                <button id="tool-set-background" class="btn btn-small tool-btn">设置背景</button>
                            </div>
                            <div class="tool-group">
                                <h4>实体工具</h4>
                                <button id="tool-add-enemy" class="btn btn-small tool-btn">放置敌人</button>
                                <button id="tool-remove-enemy" class="btn btn-small tool-btn">删除敌人</button>
                                <button id="tool-clear-enemies" class="btn btn-small tool-btn">清除所有敌人</button>
                                <button id="tool-set-player-start" class="btn btn-small tool-btn">设置玩家起点</button>
                            </div>
                            <div class="tool-group">
                                <h4>路径工具</h4>
                                <button id="tool-draw-path" class="btn btn-small tool-btn">绘制路径</button>
                                <button id="tool-clear-paths" class="btn btn-small tool-btn">清除所有路径</button>
                            </div>
                            <div class="tool-group">
                                <h4>网格设置</h4>
                                <div class="form-group">
                                    <label for="grid-size">网格大小</label>
                                    <input type="number" id="grid-size" min="20" max="100" value="80">
                                </div>
                                <div class="form-group">
                                    <label for="map-width">地图宽度</label>
                                    <input type="number" id="map-width" min="8" max="32" value="16">
                                </div>
                                <div class="form-group">
                                    <label for="map-height">地图高度</label>
                                    <input type="number" id="map-height" min="6" max="24" value="12">
                                </div>
                                <button id="apply-grid-settings" class="btn btn-small btn-primary">应用设置</button>
                            </div>
                            <div class="tool-group">
                                <h4>地图操作</h4>
                                <button id="save-map" class="btn btn-small btn-primary">保存地图</button>
                                <button id="test-map" class="btn btn-small btn-secondary">测试地图</button>
                            </div>
                        </div>
                        <div class="map-editor-canvas-container">
                            <div id="map-editor-canvas"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 精灵表管理 -->
            <section id="sprites" class="admin-section">
                <h2>精灵表管理</h2>
                <div class="section-actions">
                    <button id="create-sprite-btn" class="btn btn-primary">上传新精灵表</button>
                </div>
                <div class="sprites-grid" id="sprites-container">
                    <!-- 精灵表将通过JavaScript动态加载 -->
                </div>
            </section>

            <!-- 动画管理 -->
            <section id="animations" class="admin-section">
                <h2>动画管理</h2>
                <div class="section-actions">
                    <button id="create-animation-btn" class="btn btn-primary">创建新动画</button>
                </div>
                <div class="data-table-container">
                    <table class="data-table" id="animations-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>精灵表</th>
                                <th>类型</th>
                                <th>帧数</th>
                                <th>速度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动画数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- 游戏设置 -->
            <section id="settings" class="admin-section">
                <h2>游戏设置</h2>
                <form id="game-settings-form" class="settings-form">
                    <div class="form-group">
                        <label for="game-width">游戏宽度</label>
                        <input type="number" id="game-width" name="width" min="800" max="1920" value="1280">
                    </div>
                    <div class="form-group">
                        <label for="game-height">游戏高度</label>
                        <input type="number" id="game-height" name="height" min="600" max="1080" value="900">
                    </div>
                    <div class="form-group">
                        <label for="default-player">默认玩家</label>
                        <select id="default-player" name="defaultPlayer">
                            <!-- 玩家选项将通过JavaScript动态加载 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="enemy-spawn-rate">敌人生成速率 (秒)</label>
                        <input type="number" id="enemy-spawn-rate" name="enemySpawnRate" min="1" max="60" value="5">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">保存设置</button>
                    </div>
                </form>
            </section>
        </main>
    </div>

    <!-- 模态框 -->
    <div id="modal-container" class="modal-container">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <button id="modal-close" class="modal-close">&times;</button>
            </div>
            <div class="modal-content" id="modal-content">
                <!-- 模态框内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 精灵动画预览容器 -->
    <div id="animation-preview-container" class="animation-preview-container">
        <div class="preview-controls">
            <div class="preview-playback-controls">
                <button id="preview-play" class="btn btn-small">播放</button>
                <button id="preview-stop" class="btn btn-small">停止</button>
                <button id="preview-close" class="btn btn-small btn-danger">关闭</button>
            </div>
            <!-- 方向按钮将通过JavaScript动态添加到这里 -->
        </div>
        <div id="animation-canvas-container"></div>
    </div>

    <!-- 脚本 -->
    <script src="js/admin-utils.js"></script>
    <script src="js/database-manager.js"></script>
    <script src="js/sprite-manager.js"></script>
    <script src="js/entity-manager.js"></script>
    <script src="js/map-manager.js"></script>
    <script src="js/map-editor.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
