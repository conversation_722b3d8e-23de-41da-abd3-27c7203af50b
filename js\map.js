/**
 * 游戏地图类
 */
class GameMap {
    constructor(app, mapData = null) {
        this.app = app;
        this.container = new PIXI.Container();

        // 默认地图设置
        this.tileSize = 80; // 默认瓦片尺寸
        this.backgroundImage = null;

        // 如果提供了地图数据，使用它
        if (mapData) {
            this.loadMapData(mapData);
        } else {
            // 否则使用默认地图数据
            this.mapData = [
                [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1],
                [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1],
                [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
            ];
        }

        // 墙壁和地板的碰撞区域
        this.collisionTiles = [];

        // 敌人生成点
        this.enemySpawnPoints = [];

        // 玩家起点
        this.playerStart = { x: 1, y: 1 };

        // 路径
        this.paths = [];

        // 计算地图居中位置
        this.calculateMapPosition();

        this.init();
    }

    /**
     * 加载地图数据
     * @param {Object} mapData 地图数据
     */
    loadMapData(mapData) {
        // 保存原始地图数据
        const originalGridSize = this.tileSize;
        
        // 设置地图属性
        this.tileSize = mapData.gridSize || 80;
        this.mapData = mapData.walls || [];
        this.backgroundImage = mapData.backgroundImage || null;

        // 设置敌人生成点
        this.enemySpawnPoints = mapData.enemies || [];

        // 设置玩家起点
        this.playerStart = mapData.playerStart || { x: 1, y: 1 };

        // 设置路径
        this.paths = mapData.paths || [];

        console.log('地图数据加载成功:', mapData.name);
    }

    // 计算地图在屏幕上的居中位置
    calculateMapPosition() {
        // 使用固定的基准尺寸计算地图大小，而不是直接使用tileSize
        // 这样网格大小变化时，地图整体大小不会改变
        const baseSize = 80; // 基准网格大小
        const mapWidth = this.mapData[0].length * baseSize;
        const mapHeight = this.mapData.length * baseSize;

        // 计算固定地图大小与实际网格大小的比例
        this.mapScale = baseSize / this.tileSize;

        // 计算缩放比例以使地图完全填充画布
        const scaleX = this.app.screen.width / mapWidth;
        const scaleY = this.app.screen.height / mapHeight;
        
        // 直接使用X和Y方向的缩放比例，确保地图完全填满画布
        this.container.scale.x = scaleX;
        this.container.scale.y = scaleY;
        
        // 设置容器位置，确保地图从画布左上角开始
        this.container.x = 0;
        this.container.y = 0;
        
        // 更新偏移量为0
        this.offsetX = 0;
        this.offsetY = 0;
    }

    init() {
        // 创建地图
        this.createMap();

        // 添加到舞台
        this.app.stage.addChild(this.container);
    }

    createMap() {
        // 清空之前的地图
        while(this.container.children[0]) {
            this.container.removeChild(this.container.children[0]);
        }
        this.collisionTiles = [];

        // 使用固定的基准尺寸计算地图大小
        const baseSize = 80; // 基准网格大小
        const mapWidth = this.mapData[0].length * baseSize;
        const mapHeight = this.mapData.length * baseSize;
        
        // 计算缩放比例以使地图完全填充画布
        const scaleX = this.app.screen.width / mapWidth;
        const scaleY = this.app.screen.height / mapHeight;
        
        // 直接使用X和Y方向的缩放比例，确保地图完全填满画布
        // 不再保持比例，而是拉伸地图以完全适应画布
        this.container.scale.x = scaleX;
        this.container.scale.y = scaleY;
        
        // 设置容器位置，确保地图从画布左上角开始
        this.container.x = 0;
        this.container.y = 0;
        
        // 更新偏移量以便碰撞检测
        this.offsetX = 0;
        this.offsetY = 0;

        // 如果有背景图片，添加到容器并确保它完全填充地图
        if (this.backgroundImage) {
            const background = PIXI.Sprite.from(this.backgroundImage);
            
            // 设置背景大小以完全覆盖地图区域
            background.width = mapWidth;
            background.height = mapHeight;
            background.x = 0;
            background.y = 0;
            
            this.container.addChild(background);
        }

        // 计算实际渲染的网格大小
        const renderTileSize = baseSize;

        // 绘制地图 - 墙壁完全透明
        for (let y = 0; y < this.mapData.length; y++) {
            for (let x = 0; x < this.mapData[y].length; x++) {
                const tileType = this.mapData[y][x];

                if (tileType === 1) {
                    // 墙壁 - 完全透明，只添加碰撞区域
                    this.collisionTiles.push({
                        x: x * renderTileSize,
                        y: y * renderTileSize,
                        width: renderTileSize,
                        height: renderTileSize
                    });
                    // 不绘制任何视觉元素
                } else {
                    // 地板 - 如果有背景图片，不绘制地板
                    if (!this.backgroundImage) {
                        const tile = new PIXI.Graphics();
                        tile.beginFill(0x404040);
                        tile.drawRect(0, 0, renderTileSize, renderTileSize);
                        tile.endFill();

                        tile.x = x * renderTileSize;
                        tile.y = y * renderTileSize;
                        this.container.addChild(tile);
                    }
                }
            }
        }

        // 绘制路径
        this.paths.forEach(path => {
            if (path.points && path.points.length > 0) {
                const pathGraphics = new PIXI.Graphics();
                pathGraphics.lineStyle(renderTileSize / 10, 0x3498db, 0.5);

                // 移动到第一个点
                const firstPoint = path.points[0];
                pathGraphics.moveTo(
                    (firstPoint.x + 0.5) * renderTileSize,
                    (firstPoint.y + 0.5) * renderTileSize
                );

                // 绘制到其他点
                for (let i = 1; i < path.points.length; i++) {
                    const point = path.points[i];
                    pathGraphics.lineTo(
                        (point.x + 0.5) * renderTileSize,
                        (point.y + 0.5) * renderTileSize
                    );
                }

                this.container.addChild(pathGraphics);
            }
        });
        
        // 添加玩家起点标记（半透明）
        if (this.playerStart) {
            const startMarker = new PIXI.Graphics();
            startMarker.beginFill(0x00FF00, 0.3); // 绿色半透明
            startMarker.drawCircle(0, 0, renderTileSize / 3);
            startMarker.endFill();
            startMarker.x = (this.playerStart.x + 0.5) * renderTileSize; 
            startMarker.y = (this.playerStart.y + 0.5) * renderTileSize;
            this.container.addChild(startMarker);
        }
    }
    
    /**
     * 调整背景图片大小
     * @param {PIXI.Sprite} background 背景精灵
     * @param {number} mapWidth 地图宽度
     * @param {number} mapHeight 地图高度
     */
    adjustBackground(background, mapWidth, mapHeight) {
        // 获取原始纹理尺寸
        const origWidth = background.texture.width;
        const origHeight = background.texture.height;
        
        // 如果原始图片非常大，让我们以合适的方式缩放它
        // 保持纵横比并覆盖整个地图区域
        const scaleX = mapWidth / origWidth;
        const scaleY = mapHeight / origHeight;
        
        // 使用较大的缩放比例来确保图片覆盖整个地图区域
        const scale = Math.max(scaleX, scaleY);
        
        background.width = origWidth * scale;
        background.height = origHeight * scale;
        
        // 居中背景图片
        background.x = (mapWidth - background.width) / 2;
        background.y = (mapHeight - background.height) / 2;
    }

    // 检查碰撞 - 需要考虑新的缩放方式
    checkCollision(entity) {
        const baseSize = 80; // 基准网格大小，与createMap中使用的相同

        for (const tile of this.collisionTiles) {
            // 创建考虑地图缩放的碰撞区域，注意x和y方向可能有不同的缩放
            const adjustedTile = {
                x: tile.x * this.container.scale.x + this.container.x,
                y: tile.y * this.container.scale.y + this.container.y,
                width: tile.width * this.container.scale.x,
                height: tile.height * this.container.scale.y
            };

            if (this.isColliding(entity, adjustedTile)) {
                return true;
            }
        }
        return false;
    }

    // 简单的AABB碰撞检测
    isColliding(a, b) {
        return a.x < b.x + b.width &&
               a.x + a.width > b.x &&
               a.y < b.y + b.height &&
               a.y + a.height > b.y;
    }
}
