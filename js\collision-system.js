/**
 * 碰撞检测系统
 * 提供圆形碰撞检测、攻击范围检测、警戒范围检测等功能
 */
class CollisionSystem {
    constructor(app) {
        this.app = app;
        this.debugMode = false;
        this.debugGraphics = null;
    }

    /**
     * 启用/禁用调试模式
     * @param {boolean} enabled 是否启用调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;

        if (enabled && !this.debugGraphics) {
            this.debugGraphics = new PIXI.Graphics();
            this.app.stage.addChild(this.debugGraphics);
        } else if (!enabled && this.debugGraphics) {
            this.app.stage.removeChild(this.debugGraphics);
            this.debugGraphics = null;
        }
    }

    /**
     * 清除调试图形
     */
    clearDebugGraphics() {
        if (this.debugGraphics) {
            this.debugGraphics.clear();
        }
    }

    /**
     * 绘制调试圆形
     * @param {number} x 中心X坐标
     * @param {number} y 中心Y坐标
     * @param {number} radius 半径
     * @param {number} color 颜色
     * @param {number} alpha 透明度
     */
    drawDebugCircle(x, y, radius, color = 0xff0000, alpha = 0.3) {
        if (!this.debugMode || !this.debugGraphics) return;

        this.debugGraphics.lineStyle(2, color, 1);
        this.debugGraphics.beginFill(color, alpha);
        this.debugGraphics.drawCircle(x, y, radius);
        this.debugGraphics.endFill();
    }

    /**
     * 获取实体的碰撞框信息
     * @param {Object} entity 实体对象
     * @returns {Object} 碰撞框信息
     */
    getEntityCollisionBox(entity) {
        // 获取碰撞框配置，如果没有则使用默认值
        const collisionBox = entity.collisionBox || {
            width: 40,
            height: 40,
            offsetX: 0,
            offsetY: 0
        };

        // 计算碰撞框中心位置
        const centerX = entity.x + (entity.defaultAnimationSize?.width || 60) / 2 + (collisionBox.offsetX || 0);
        const centerY = entity.y + (entity.defaultAnimationSize?.height || 60) / 2 + (collisionBox.offsetY || 0);

        // 使用宽度和高度的最大值作为半径
        const radius = Math.max(collisionBox.width, collisionBox.height) / 2;

        return {
            x: centerX,
            y: centerY,
            radius: radius,
            offsetX: collisionBox.offsetX || 0,
            offsetY: collisionBox.offsetY || 0
        };
    }

    /**
     * 获取实体的攻击范围信息
     * @param {Object} entity 实体对象
     * @returns {Object} 攻击范围信息
     */
    getEntityAttackRange(entity) {
        let attackRange;

        if (entity.attackRange) {
            // 如果实体有自定义攻击范围，使用它
            attackRange = entity.attackRange;
        } else {
            // 否则基于实体动画尺寸计算合理的近战攻击范围
            const entitySize = entity.defaultAnimationSize || { width: 50, height: 50 };
            // 近战攻击范围应该很小，只比实体本身稍大一点点，符合近战攻击的视觉效果
            const extraReach = 10; // 减少到10像素，让攻击距离更短更真实
            attackRange = {
                width: entitySize.width + extraReach,
                height: entitySize.height + extraReach
            };
        }

        const collisionBox = this.getEntityCollisionBox(entity);

        // 攻击范围跟随碰撞框移动
        const centerX = collisionBox.x;
        const centerY = collisionBox.y;
        const radius = Math.max(attackRange.width, attackRange.height) / 2;

        return {
            x: centerX,
            y: centerY,
            radius: radius
        };
    }

    /**
     * 获取实体的技能攻击范围信息
     * @param {Object} entity 实体对象
     * @returns {Object} 技能攻击范围信息
     */
    getEntitySkillRange(entity) {
        let skillRange;

        if (entity.skillRange) {
            // 如果实体有自定义技能攻击范围，使用它
            skillRange = entity.skillRange;
        } else {
            // 否则基于实体尺寸计算技能攻击范围
            const entitySize = entity.defaultAnimationSize || { width: 50, height: 50 };
            // 技能攻击范围比普通攻击稍大，但仍然是近战范围
            const extraReach = 20; // 技能攻击比普通攻击多10像素
            skillRange = {
                width: entitySize.width + extraReach,
                height: entitySize.height + extraReach
            };
        }

        const collisionBox = this.getEntityCollisionBox(entity);

        // 技能攻击范围跟随碰撞框移动
        const centerX = collisionBox.x;
        const centerY = collisionBox.y;
        const radius = Math.max(skillRange.width, skillRange.height) / 2;

        return {
            x: centerX,
            y: centerY,
            radius: radius
        };
    }

    /**
     * 获取实体的警戒范围信息（仅敌人）
     * @param {Object} entity 实体对象
     * @returns {Object} 警戒范围信息
     */
    getEntityAlertRange(entity) {
        let alertRange;

        if (entity.alertRange) {
            // 如果实体有自定义警戒范围，使用它
            alertRange = entity.alertRange;
        } else {
            // 否则基于实体尺寸计算合理的警戒范围
            const entitySize = entity.defaultAnimationSize || { width: 50, height: 50 };
            // 警戒范围应该比攻击范围大很多，让敌人能提前发现玩家
            const alertReach = 80; // 警戒范围比实体大80像素
            alertRange = {
                width: entitySize.width + alertReach,
                height: entitySize.height + alertReach
            };
        }

        const collisionBox = this.getEntityCollisionBox(entity);

        // 警戒范围跟随碰撞框移动
        const centerX = collisionBox.x;
        const centerY = collisionBox.y;
        const radius = Math.max(alertRange.width, alertRange.height) / 2;

        return {
            x: centerX,
            y: centerY,
            radius: radius
        };
    }

    /**
     * 检测两个圆形是否碰撞
     * @param {Object} circle1 第一个圆形 {x, y, radius}
     * @param {Object} circle2 第二个圆形 {x, y, radius}
     * @returns {boolean} 是否碰撞
     */
    checkCircleCollision(circle1, circle2) {
        const dx = circle1.x - circle2.x;
        const dy = circle1.y - circle2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < (circle1.radius + circle2.radius);
    }

    /**
     * 检测实体之间的碰撞
     * @param {Object} entity1 第一个实体
     * @param {Object} entity2 第二个实体
     * @returns {boolean} 是否碰撞
     */
    checkEntityCollision(entity1, entity2) {
        const box1 = this.getEntityCollisionBox(entity1);
        const box2 = this.getEntityCollisionBox(entity2);

        if (this.debugMode) {
            this.drawDebugCircle(box1.x, box1.y, box1.radius, 0xff0000, 0.2);
            this.drawDebugCircle(box2.x, box2.y, box2.radius, 0xff0000, 0.2);
        }

        return this.checkCircleCollision(box1, box2);
    }

    /**
     * 检测攻击是否命中目标
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 是否命中
     */
    checkAttackHit(attacker, target) {
        const attackRange = this.getEntityAttackRange(attacker);
        const targetBox = this.getEntityCollisionBox(target);

        if (this.debugMode) {
            this.drawDebugCircle(attackRange.x, attackRange.y, attackRange.radius, 0xff8800, 0.2);
            this.drawDebugCircle(targetBox.x, targetBox.y, targetBox.radius, 0xff0000, 0.2);
        }

        return this.checkCircleCollision(attackRange, targetBox);
    }

    /**
     * 检测技能攻击是否命中目标
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 是否命中
     */
    checkSkillHit(attacker, target) {
        const skillRange = this.getEntitySkillRange(attacker);
        const targetBox = this.getEntityCollisionBox(target);

        if (this.debugMode) {
            this.drawDebugCircle(skillRange.x, skillRange.y, skillRange.radius, 0xff4400, 0.2);
            this.drawDebugCircle(targetBox.x, targetBox.y, targetBox.radius, 0xff0000, 0.2);
        }

        return this.checkCircleCollision(skillRange, targetBox);
    }

    /**
     * 检测目标是否在警戒范围内
     * @param {Object} entity 实体（通常是敌人）
     * @param {Object} target 目标（通常是玩家）
     * @returns {boolean} 是否在警戒范围内
     */
    checkInAlertRange(entity, target) {
        const alertRange = this.getEntityAlertRange(entity);
        const targetBox = this.getEntityCollisionBox(target);

        if (this.debugMode) {
            this.drawDebugCircle(alertRange.x, alertRange.y, alertRange.radius, 0x8800ff, 0.1);
            this.drawDebugCircle(targetBox.x, targetBox.y, targetBox.radius, 0xff0000, 0.2);
        }

        return this.checkCircleCollision(alertRange, targetBox);
    }

    /**
     * 检测实体与地图墙壁的碰撞
     * @param {Object} entity 实体
     * @param {Object} gameMap 游戏地图
     * @returns {boolean} 是否与墙壁碰撞
     */
    checkWallCollision(entity, gameMap) {
        const box = this.getEntityCollisionBox(entity);

        // 创建一个矩形边界框用于地图碰撞检测
        const bounds = {
            x: box.x - box.radius,
            y: box.y - box.radius,
            width: box.radius * 2,
            height: box.radius * 2
        };

        if (this.debugMode) {
            this.drawDebugCircle(box.x, box.y, box.radius, 0x00ff00, 0.2);
        }

        return gameMap.checkCollision(bounds);
    }

    /**
     * 获取两个实体之间的距离
     * @param {Object} entity1 第一个实体
     * @param {Object} entity2 第二个实体
     * @returns {number} 距离
     */
    getDistance(entity1, entity2) {
        const box1 = this.getEntityCollisionBox(entity1);
        const box2 = this.getEntityCollisionBox(entity2);

        const dx = box1.x - box2.x;
        const dy = box1.y - box2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 获取从实体1到实体2的方向向量
     * @param {Object} entity1 起始实体
     * @param {Object} entity2 目标实体
     * @returns {Object} 方向向量 {x, y}
     */
    getDirection(entity1, entity2) {
        const box1 = this.getEntityCollisionBox(entity1);
        const box2 = this.getEntityCollisionBox(entity2);

        const dx = box2.x - box1.x;
        const dy = box2.y - box1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance === 0) return { x: 0, y: 0 };

        return {
            x: dx / distance,
            y: dy / distance
        };
    }
}
