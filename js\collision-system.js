/**
 * 碰撞检测系统
 * 提供圆形碰撞检测、攻击范围检测、警戒范围检测等功能
 */
class CollisionSystem {
    constructor(app) {
        this.app = app;
        this.debugMode = false;
        this.debugGraphics = null;
    }

    /**
     * 启用/禁用调试模式
     * @param {boolean} enabled 是否启用调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;

        if (enabled && !this.debugGraphics) {
            this.debugGraphics = new PIXI.Graphics();
            // 将调试图形层添加到舞台，确保在实体之上
            this.app.stage.addChild(this.debugGraphics);
            // 设置较高的zIndex确保在实体之上显示
            this.debugGraphics.zIndex = 1000;
        } else if (!enabled && this.debugGraphics) {
            this.app.stage.removeChild(this.debugGraphics);
            this.debugGraphics = null;
        }
    }

    /**
     * 清除调试图形
     */
    clearDebugGraphics() {
        if (this.debugGraphics) {
            this.debugGraphics.clear();
        }
    }

    /**
     * 绘制调试圆形
     * @param {number} x 中心X坐标
     * @param {number} y 中心Y坐标
     * @param {number} radius 半径
     * @param {number} color 颜色
     * @param {number} alpha 透明度
     * @param {boolean} fillOnly 是否只填充不绘制边框
     */
    drawDebugCircle(x, y, radius, color = 0xff0000, alpha = 0.3, fillOnly = false) {
        if (!this.debugMode || !this.debugGraphics) return;

        if (!fillOnly) {
            this.debugGraphics.lineStyle(2, color, 1);
        }
        this.debugGraphics.beginFill(color, alpha);
        this.debugGraphics.drawCircle(x, y, radius);
        this.debugGraphics.endFill();
    }

    /**
     * 绘制调试矩形
     * @param {number} x 矩形左上角X坐标
     * @param {number} y 矩形左上角Y坐标
     * @param {number} width 矩形宽度
     * @param {number} height 矩形高度
     * @param {number} color 颜色
     * @param {number} alpha 透明度
     */
    drawDebugRectangle(x, y, width, height, color, alpha) {
        if (!this.debugMode || !this.debugGraphics) return;

        this.debugGraphics.beginFill(color, alpha);
        this.debugGraphics.drawRect(x, y, width, height);
        this.debugGraphics.endFill();
    }

    /**
     * 绘制实体的调试信息（碰撞框和攻击范围）
     * @param {Object} entity 实体对象
     */
    drawEntityDebugInfo(entity) {
        if (!this.debugMode || !this.debugGraphics) return;

        // 绘制碰撞框（红色边框）
        const collisionBox = this.getEntityCollisionBox(entity);
        this.debugGraphics.lineStyle(2, 0xff0000, 1);
        this.debugGraphics.beginFill(0xff0000, 0.1);
        this.debugGraphics.drawRect(collisionBox.x, collisionBox.y, collisionBox.width, collisionBox.height);
        this.debugGraphics.endFill();

        // 绘制攻击范围（橙色圆圈）
        const attackRange = this.getEntityAttackRange(entity);
        this.debugGraphics.lineStyle(2, 0xff8800, 0.8);
        this.debugGraphics.beginFill(0xff8800, 0.1);
        this.debugGraphics.drawCircle(attackRange.x, attackRange.y, attackRange.radius);
        this.debugGraphics.endFill();

        // 如果是敌人，还要绘制警戒范围（紫色圆圈）
        if (entity.type === 'enemy') {
            const alertRange = this.getEntityAlertRange(entity);
            this.debugGraphics.lineStyle(2, 0x9b59b6, 0.6);
            this.debugGraphics.beginFill(0x9b59b6, 0.05);
            this.debugGraphics.drawCircle(alertRange.x, alertRange.y, alertRange.radius);
            this.debugGraphics.endFill();
        }
    }

    /**
     * 获取实体的碰撞框信息
     * @param {Object} entity 实体对象
     * @returns {Object} 碰撞框信息
     */
    getEntityCollisionBox(entity) {
        // 获取碰撞框配置，如果没有则使用默认值
        const collisionBox = entity.collisionBox || {
            width: 40,
            height: 40,
            offsetX: 0,
            offsetY: 0
        };

        // 计算动画中心位置
        const animationCenterX = entity.x + (entity.defaultAnimationSize?.width || 60) / 2;
        const animationCenterY = entity.y + (entity.defaultAnimationSize?.height || 60) / 2;

        // 碰撞框的左上角位置（在动画正中间，考虑偏移）
        const boxX = animationCenterX - collisionBox.width / 2 + (collisionBox.offsetX || 0);
        const boxY = animationCenterY - collisionBox.height / 2 + (collisionBox.offsetY || 0);

        // 碰撞框的中心位置
        const centerX = animationCenterX + (collisionBox.offsetX || 0);
        const centerY = animationCenterY + (collisionBox.offsetY || 0);

        return {
            x: boxX,
            y: boxY,
            width: collisionBox.width,
            height: collisionBox.height,
            centerX: centerX,
            centerY: centerY,
            offsetX: collisionBox.offsetX || 0,
            offsetY: collisionBox.offsetY || 0
        };
    }

    /**
     * 获取实体的攻击范围信息
     * @param {Object} entity 实体对象
     * @returns {Object} 攻击范围信息
     */
    getEntityAttackRange(entity) {
        let attackRange;

        if (entity.attackRange) {
            // 如果实体有自定义攻击范围，使用它
            attackRange = entity.attackRange;
        } else {
            // 否则基于实体动画尺寸计算合理的攻击范围，符合攻击动画效果
            const entitySize = entity.defaultAnimationSize || { width: 50, height: 50 };
            // 攻击范围应该符合攻击动画的视觉效果，让玩家感觉合理
            const extraReach = 35; // 增加到35像素，符合攻击动画的挥击范围
            attackRange = {
                width: entitySize.width + extraReach,
                height: entitySize.height + extraReach
            };
        }

        const collisionBox = this.getEntityCollisionBox(entity);

        // 攻击范围跟随碰撞框中心
        const centerX = collisionBox.centerX;
        const centerY = collisionBox.centerY;
        const radius = Math.max(attackRange.width, attackRange.height) / 2;

        return {
            x: centerX,
            y: centerY,
            radius: radius
        };
    }

    /**
     * 获取实体的技能攻击范围信息
     * @param {Object} entity 实体对象
     * @returns {Object} 技能攻击范围信息
     */
    getEntitySkillRange(entity) {
        let skillRange;

        if (entity.skillRange) {
            // 如果实体有自定义技能攻击范围，使用它
            skillRange = entity.skillRange;
        } else {
            // 否则基于实体尺寸计算技能攻击范围，符合技能动画效果
            const entitySize = entity.defaultAnimationSize || { width: 50, height: 50 };
            // 技能攻击范围比普通攻击更大，符合技能动画的特殊效果
            const extraReach = 55; // 技能攻击比普通攻击多20像素，总共55像素
            skillRange = {
                width: entitySize.width + extraReach,
                height: entitySize.height + extraReach
            };
        }

        const collisionBox = this.getEntityCollisionBox(entity);

        // 技能攻击范围跟随碰撞框中心
        const centerX = collisionBox.centerX;
        const centerY = collisionBox.centerY;
        const radius = Math.max(skillRange.width, skillRange.height) / 2;

        return {
            x: centerX,
            y: centerY,
            radius: radius
        };
    }

    /**
     * 获取实体的警戒范围信息（仅敌人）
     * @param {Object} entity 实体对象
     * @returns {Object} 警戒范围信息
     */
    getEntityAlertRange(entity) {
        let alertRange;

        if (entity.alertRange) {
            // 如果实体有自定义警戒范围，使用它
            alertRange = entity.alertRange;
        } else {
            // 否则基于实体尺寸计算合理的警戒范围
            const entitySize = entity.defaultAnimationSize || { width: 50, height: 50 };
            // 警戒范围应该比攻击范围大很多，让敌人能提前发现玩家
            const alertReach = 80; // 警戒范围比实体大80像素
            alertRange = {
                width: entitySize.width + alertReach,
                height: entitySize.height + alertReach
            };
        }

        const collisionBox = this.getEntityCollisionBox(entity);

        // 警戒范围跟随碰撞框中心
        const centerX = collisionBox.centerX;
        const centerY = collisionBox.centerY;
        const radius = Math.max(alertRange.width, alertRange.height) / 2;

        return {
            x: centerX,
            y: centerY,
            radius: radius
        };
    }

    /**
     * 检测两个矩形是否碰撞
     * @param {Object} rect1 第一个矩形 {x, y, width, height}
     * @param {Object} rect2 第二个矩形 {x, y, width, height}
     * @returns {boolean} 是否碰撞
     */
    checkRectangleCollision(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }

    /**
     * 检测两个圆形是否碰撞
     * @param {Object} circle1 第一个圆形 {x, y, radius}
     * @param {Object} circle2 第二个圆形 {x, y, radius}
     * @returns {boolean} 是否碰撞
     */
    checkCircleCollision(circle1, circle2) {
        const dx = circle1.x - circle2.x;
        const dy = circle1.y - circle2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < (circle1.radius + circle2.radius);
    }

    /**
     * 检测矩形和圆形是否碰撞
     * @param {Object} rect 矩形 {x, y, width, height}
     * @param {Object} circle 圆形 {x, y, radius}
     * @returns {boolean} 是否碰撞
     */
    checkRectangleCircleCollision(rect, circle) {
        // 找到矩形上距离圆心最近的点
        const closestX = Math.max(rect.x, Math.min(circle.x, rect.x + rect.width));
        const closestY = Math.max(rect.y, Math.min(circle.y, rect.y + rect.height));

        // 计算距离
        const dx = circle.x - closestX;
        const dy = circle.y - closestY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        return distance < circle.radius;
    }

    /**
     * 检测实体之间的碰撞（矩形碰撞）
     * @param {Object} entity1 第一个实体
     * @param {Object} entity2 第二个实体
     * @returns {boolean} 是否碰撞
     */
    checkEntityCollision(entity1, entity2) {
        const box1 = this.getEntityCollisionBox(entity1);
        const box2 = this.getEntityCollisionBox(entity2);

        if (this.debugMode) {
            this.drawDebugRectangle(box1.x, box1.y, box1.width, box1.height, 0xff0000, 0.2);
            this.drawDebugRectangle(box2.x, box2.y, box2.width, box2.height, 0xff0000, 0.2);
        }

        return this.checkRectangleCollision(box1, box2);
    }

    /**
     * 检测攻击是否命中目标（圆形攻击范围 vs 矩形碰撞框）
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 是否命中
     */
    checkAttackHit(attacker, target) {
        const attackRange = this.getEntityAttackRange(attacker);
        const targetBox = this.getEntityCollisionBox(target);

        if (this.debugMode) {
            this.drawDebugCircle(attackRange.x, attackRange.y, attackRange.radius, 0xff8800, 0.2);
            this.drawDebugRectangle(targetBox.x, targetBox.y, targetBox.width, targetBox.height, 0xff0000, 0.2);
        }

        return this.checkRectangleCircleCollision(targetBox, attackRange);
    }

    /**
     * 检测技能攻击是否命中目标（圆形技能范围 vs 矩形碰撞框）
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 是否命中
     */
    checkSkillHit(attacker, target) {
        const skillRange = this.getEntitySkillRange(attacker);
        const targetBox = this.getEntityCollisionBox(target);

        if (this.debugMode) {
            this.drawDebugCircle(skillRange.x, skillRange.y, skillRange.radius, 0xff4400, 0.2);
            this.drawDebugRectangle(targetBox.x, targetBox.y, targetBox.width, targetBox.height, 0xff0000, 0.2);
        }

        return this.checkRectangleCircleCollision(targetBox, skillRange);
    }

    /**
     * 检测目标是否在警戒范围内（圆形警戒范围 vs 矩形碰撞框）
     * @param {Object} entity 实体（通常是敌人）
     * @param {Object} target 目标（通常是玩家）
     * @returns {boolean} 是否在警戒范围内
     */
    checkInAlertRange(entity, target) {
        const alertRange = this.getEntityAlertRange(entity);
        const targetBox = this.getEntityCollisionBox(target);

        if (this.debugMode) {
            this.drawDebugCircle(alertRange.x, alertRange.y, alertRange.radius, 0x8800ff, 0.1);
            this.drawDebugRectangle(targetBox.x, targetBox.y, targetBox.width, targetBox.height, 0xff0000, 0.2);
        }

        return this.checkRectangleCircleCollision(targetBox, alertRange);
    }

    /**
     * 检测实体与地图墙壁的碰撞（矩形碰撞框）
     * @param {Object} entity 实体
     * @param {Object} gameMap 游戏地图
     * @returns {boolean} 是否与墙壁碰撞
     */
    checkWallCollision(entity, gameMap) {
        const box = this.getEntityCollisionBox(entity);

        // 直接使用矩形碰撞框进行地图碰撞检测
        const bounds = {
            x: box.x,
            y: box.y,
            width: box.width,
            height: box.height
        };

        if (this.debugMode) {
            this.drawDebugRectangle(box.x, box.y, box.width, box.height, 0x00ff00, 0.2);
        }

        return gameMap.checkCollision(bounds);
    }

    /**
     * 获取两个实体之间的距离（基于碰撞框中心）
     * @param {Object} entity1 第一个实体
     * @param {Object} entity2 第二个实体
     * @returns {number} 距离
     */
    getDistance(entity1, entity2) {
        const box1 = this.getEntityCollisionBox(entity1);
        const box2 = this.getEntityCollisionBox(entity2);

        const dx = box1.centerX - box2.centerX;
        const dy = box1.centerY - box2.centerY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 获取从实体1到实体2的方向向量（基于碰撞框中心）
     * @param {Object} entity1 起始实体
     * @param {Object} entity2 目标实体
     * @returns {Object} 方向向量 {x, y}
     */
    getDirection(entity1, entity2) {
        const box1 = this.getEntityCollisionBox(entity1);
        const box2 = this.getEntityCollisionBox(entity2);

        const dx = box2.centerX - box1.centerX;
        const dy = box2.centerY - box1.centerY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance === 0) return { x: 0, y: 0 };

        return {
            x: dx / distance,
            y: dy / distance
        };
    }
}
