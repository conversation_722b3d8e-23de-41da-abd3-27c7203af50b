<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战斗系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #2c3e50;
            color: white;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #ecf0f1;
            margin-bottom: 20px;
        }

        .controls {
            background-color: #34495e;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .controls button:hover {
            background-color: #2980b9;
        }

        .controls button.active {
            background-color: #e74c3c;
        }

        .game-container {
            background-color: #34495e;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }

        .instructions {
            background-color: #34495e;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .instructions h3 {
            color: #3498db;
            margin-top: 0;
        }

        .instructions ul {
            text-align: left;
            max-width: 600px;
            margin: 0 auto;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .key {
            background-color: #2c3e50;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            color: #3498db;
        }

        #gameContainer {
            width: 100%;
            height: 600px;
            border: 2px solid #3498db;
            border-radius: 5px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>2D冒险游戏 - 战斗系统测试</h1>

        <div class="controls">
            <button id="debugBtn">切换调试模式</button>
            <button id="reloadBtn">重新加载实体</button>
            <button id="reviveBtn">复活玩家</button>
            <span>调试模式: <span id="debugStatus">关闭</span></span>
        </div>

        <div class="game-container">
            <div id="gameContainer"></div>
        </div>

        <div class="instructions">
            <h3>操作说明</h3>
            <ul>
                <li><span class="key">WASD</span> 或 <span class="key">方向键</span> - 移动玩家</li>
                <li><span class="key">空格</span> - 普通攻击（近战攻击，范围基于动画效果，需要接近敌人）</li>
                <li><span class="key">Q</span> - 技能攻击（近战技能，范围比普通攻击稍大，两段伤害，总伤害300%，冷却时间更长）</li>
                <li><span class="key">R</span> - 复活（仅在死亡时可用）</li>
            </ul>

            <h3>战斗系统特性</h3>
            <ul>
                <li><strong>动画系统</strong>：攻击、技能攻击、死亡、复活动画，动画连贯流畅</li>
                <li><strong>近战攻击机制</strong>：攻击距离基于动画效果，需要接近敌人才能造成伤害</li>
                <li><strong>合理攻击距离</strong>：普通攻击距离（+35像素），技能攻击距离更远（+55像素），符合动画效果</li>
                <li><strong>AI系统</strong>：敌人有巡逻、警戒、追击、攻击四种状态，会根据不同攻击范围调整行为</li>
                <li><strong>碰撞检测</strong>：基于矩形的精确碰撞检测，碰撞框位于动画正中间</li>
                <li><strong>攻击冷却</strong>：普通攻击1秒冷却，技能攻击3秒冷却</li>
                <li><strong>伤害计算</strong>：基于攻击力和防御力，玩家技能攻击为两段伤害，总伤害300%</li>
                <li><strong>调试模式</strong>：可视化显示矩形碰撞框（红色）、圆形攻击范围（橙色）、圆形技能攻击范围（红橙色）、圆形警戒范围（紫色）</li>
            </ul>

            <h3>技能攻击机制</h3>
            <ul>
                <li><strong>玩家技能攻击</strong>：两段伤害，每段150%，总伤害300%</li>
                <li><strong>伤害间隔</strong>：第二段伤害在400毫秒后触发</li>
                <li><strong>伤害颜色</strong>：第一段橙色，第二段深橙色</li>
                <li><strong>敌人技能攻击</strong>：单段伤害，150%伤害（保持原有机制）</li>
            </ul>

            <h3>敌人复活机制</h3>
            <ul>
                <li><strong>自动复活</strong>：普通骷髅死亡后60秒自动复活</li>
                <li><strong>复活动画</strong>：复活时播放复活动画，然后恢复正常状态</li>
                <li><strong>状态重置</strong>：复活后AI状态重置为巡逻，生命值恢复满血</li>
                <li><strong>复活日志</strong>：控制台会显示复活倒计时和复活过程</li>
            </ul>

            <h3>敌人AI行为</h3>
            <ul>
                <li><strong>巡逻</strong>：沿路径移动或随机移动</li>
                <li><strong>警戒</strong>：发现玩家后面向玩家观察</li>
                <li><strong>追击</strong>：追击进入警戒范围的玩家</li>
                <li><strong>攻击</strong>：在攻击范围内攻击玩家，随机使用普通攻击或技能攻击</li>
            </ul>
        </div>
    </div>

    <!-- PIXI.js -->
    <script src="https://pixijs.download/release/pixi.min.js"></script>

    <!-- 游戏脚本 -->
    <script src="js/database-manager.js"></script>
    <script src="js/sprite-manager.js"></script>
    <script src="js/entity-manager.js"></script>
    <script src="js/collision-system.js"></script>
    <script src="js/combat-system.js"></script>
    <script src="js/map.js"></script>
    <script src="js/player.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/game.js"></script>

    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 设置按钮事件
            const debugBtn = document.getElementById('debugBtn');
            const reloadBtn = document.getElementById('reloadBtn');
            const reviveBtn = document.getElementById('reviveBtn');
            const debugStatus = document.getElementById('debugStatus');

            debugBtn.addEventListener('click', function() {
                if (window.game && window.game.collisionSystem) {
                    const currentMode = window.game.collisionSystem.debugMode;
                    window.game.collisionSystem.setDebugMode(!currentMode);
                    debugStatus.textContent = !currentMode ? '开启' : '关闭';
                    debugBtn.classList.toggle('active', !currentMode);
                    console.log(`调试模式: ${!currentMode ? '开启' : '关闭'}`);
                }
            });

            reloadBtn.addEventListener('click', async function() {
                if (window.game) {
                    console.log('重新加载实体...');

                    // 移除现有实体
                    if (window.game.player && window.game.player.sprite) {
                        window.game.app.stage.removeChild(window.game.player.sprite);
                    }

                    for (const enemy of window.game.enemies) {
                        if (enemy.sprite) {
                            window.game.app.stage.removeChild(enemy.sprite);
                        }
                    }

                    // 重新创建实体
                    await window.game.createPlayer();
                    await window.game.createEnemies();

                    console.log('实体重新加载完成');
                }
            });

            reviveBtn.addEventListener('click', function() {
                if (window.game && window.game.player && window.game.combatSystem) {
                    if (window.game.combatSystem.isDead(window.game.player)) {
                        window.game.combatSystem.reviveEntity(window.game.player);
                        console.log('玩家已复活');
                    } else {
                        console.log('玩家还活着，无需复活');
                    }
                }
            });
        });
    </script>
</body>
</html>
