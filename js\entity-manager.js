/**
 * 实体管理类
 * 用于管理游戏中的玩家和敌人实体
 */
class EntityManager {
    constructor(dbManager, spriteManager) {
        this.dbManager = dbManager;
        this.spriteManager = spriteManager;
        this.players = {};
        this.enemies = {};

        // 实体类型
        this.entityTypes = {
            PLAYER: 'player',
            ENEMY: 'enemy'
        };
    }

    /**
     * 初始化实体管理器
     */
    async initialize() {
        try {
            // 加载所有玩家数据
            const players = await this.dbManager.getAllData(this.dbManager.stores.players);
            players.forEach(player => {
                this.players[player.id] = player;
            });

            // 加载所有敌人数据
            const enemies = await this.dbManager.getAllData(this.dbManager.stores.enemies);
            enemies.forEach(enemy => {
                this.enemies[enemy.id] = enemy;
            });

            console.log('实体管理器初始化完成');
            return true;
        } catch (error) {
            console.error('实体管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 创建新的玩家
     * @param {Object} playerData 玩家数据
     * @returns {Promise} 创建操作的Promise
     */
    async createPlayer(playerData) {
        try {
            // 确保类型正确
            playerData.type = this.entityTypes.PLAYER;

            const id = await this.dbManager.addData(this.dbManager.stores.players, playerData);
            playerData.id = id;
            this.players[id] = playerData;
            return id;
        } catch (error) {
            console.error('创建玩家失败:', error);
            throw error;
        }
    }

    /**
     * 更新玩家
     * @param {Object} playerData 玩家数据
     * @returns {Promise} 更新操作的Promise
     */
    async updatePlayer(playerData) {
        try {
            await this.dbManager.updateData(this.dbManager.stores.players, playerData);
            this.players[playerData.id] = playerData;
            return playerData;
        } catch (error) {
            console.error('更新玩家失败:', error);
            throw error;
        }
    }

    /**
     * 删除玩家
     * @param {number} id 玩家ID
     * @returns {Promise} 删除操作的Promise
     */
    async deletePlayer(id) {
        try {
            await this.dbManager.deleteData(this.dbManager.stores.players, id);
            delete this.players[id];
            return true;
        } catch (error) {
            console.error('删除玩家失败:', error);
            throw error;
        }
    }

    /**
     * 创建新的敌人
     * @param {Object} enemyData 敌人数据
     * @returns {Promise} 创建操作的Promise
     */
    async createEnemy(enemyData) {
        try {
            // 确保类型正确
            enemyData.type = this.entityTypes.ENEMY;

            const id = await this.dbManager.addData(this.dbManager.stores.enemies, enemyData);
            enemyData.id = id;
            this.enemies[id] = enemyData;
            return id;
        } catch (error) {
            console.error('创建敌人失败:', error);
            throw error;
        }
    }

    /**
     * 更新敌人
     * @param {Object} enemyData 敌人数据
     * @returns {Promise} 更新操作的Promise
     */
    async updateEnemy(enemyData) {
        try {
            await this.dbManager.updateData(this.dbManager.stores.enemies, enemyData);
            this.enemies[enemyData.id] = enemyData;
            return enemyData;
        } catch (error) {
            console.error('更新敌人失败:', error);
            throw error;
        }
    }

    /**
     * 删除敌人
     * @param {number} id 敌人ID
     * @returns {Promise} 删除操作的Promise
     */
    async deleteEnemy(id) {
        try {
            await this.dbManager.deleteData(this.dbManager.stores.enemies, id);
            delete this.enemies[id];
            return true;
        } catch (error) {
            console.error('删除敌人失败:', error);
            throw error;
        }
    }

    /**
     * 创建游戏实体
     * @param {string} type 实体类型 ('player' 或 'enemy')
     * @param {number} id 实体ID
     * @returns {Object} 游戏实体对象
     */
    async createGameEntity(type, id) {
        try {
            console.log(`创建${type}实体，ID: ${id}`);

            let entityData;

            if (type === this.entityTypes.PLAYER) {
                entityData = this.players[id];
                if (!entityData) {
                    throw new Error(`未找到ID为${id}的玩家`);
                }
                console.log('找到玩家数据:', entityData);
            } else if (type === this.entityTypes.ENEMY) {
                entityData = this.enemies[id];
                if (!entityData) {
                    throw new Error(`未找到ID为${id}的敌人`);
                }
                console.log('找到敌人数据:', entityData);
            } else {
                throw new Error(`未知的实体类型: ${type}`);
            }

            // 创建实体对象
            const entity = {
                id: entityData.id,
                name: entityData.name,
                type: entityData.type,
                health: entityData.health,
                maxHealth: entityData.maxHealth,
                speed: entityData.speed,
                attack: entityData.attack,
                defense: entityData.defense,
                x: entityData.x || 0,
                y: entityData.y || 0,
                // 以下属性不再使用整体的宽度和高度
                // width: entityData.width,
                // height: entityData.height,
                // 添加动画尺寸相关属性
                defaultAnimationWidth: entityData.defaultAnimationWidth,
                defaultAnimationHeight: entityData.defaultAnimationHeight,
                // 拷贝动画尺寸设置
                animationSizes: entityData.animationSizes ? JSON.parse(JSON.stringify(entityData.animationSizes)) : {},
                animations: {},
                currentAnimation: null,
                direction: 'down',

                // 方法
                setDirection(direction) {
                    this.direction = direction;
                    this.updateAnimation();
                },

                setAnimation(animationType) {
                    if (this.animations[animationType]) {
                        if (this.currentAnimation) {
                            this.currentAnimation.stop();
                        }
                        this.currentAnimation = this.animations[animationType];
                        this.currentAnimation.play();
                    }
                },

                updateAnimation() {
                    // 根据方向更新当前动画
                    if (this.currentAnimation) {
                        // 这里需要实现方向切换逻辑
                    }
                },

                update() {
                    // 更新实体状态
                    if (this.sprite) {
                        this.sprite.x = this.x;
                        this.sprite.y = this.y;
                    }
                }
            };

            // 复制动画数据
            if (entityData.animations) {
                console.log(`实体有 ${Object.keys(entityData.animations).length} 个动画配置`);

                // 直接复制动画ID
                entity.animations = JSON.parse(JSON.stringify(entityData.animations));

                console.log('复制的动画数据:', entity.animations);
            } else {
                console.log('实体没有动画配置');
            }

            return entity;
        } catch (error) {
            console.error('创建游戏实体失败:', error);
            throw error;
        }
    }
}
