<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PIXI.js 调试测试</title>
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@7.x/dist/pixi.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .btn {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        #pixi-container {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PIXI.js 调试测试</h1>
        
        <div class="test-section">
            <h2>1. PIXI.js 加载检测</h2>
            <div id="pixi-status" class="status">检测中...</div>
        </div>
        
        <div class="test-section">
            <h2>2. PIXI 应用创建测试</h2>
            <button id="create-app-btn" class="btn">创建 PIXI 应用</button>
            <div id="app-status" class="status">等待测试...</div>
            <div id="pixi-container"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 图形绘制测试</h2>
            <button id="draw-graphics-btn" class="btn">绘制图形</button>
            <div id="graphics-status" class="status">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h2>4. 范围预览测试</h2>
            <button id="test-ranges-btn" class="btn">测试范围预览</button>
            <div id="ranges-status" class="status">等待测试...</div>
        </div>
    </div>

    <script>
        let app = null;

        // 检测PIXI.js是否加载
        function checkPixiLoaded() {
            const statusDiv = document.getElementById('pixi-status');
            if (typeof PIXI !== 'undefined') {
                statusDiv.textContent = `✅ PIXI.js 已加载 (版本: ${PIXI.VERSION})`;
                statusDiv.className = 'status success';
                console.log('PIXI.js 版本:', PIXI.VERSION);
                console.log('PIXI 对象:', PIXI);
            } else {
                statusDiv.textContent = '❌ PIXI.js 未加载';
                statusDiv.className = 'status error';
            }
        }

        // 创建PIXI应用
        function createPixiApp() {
            const statusDiv = document.getElementById('app-status');
            const container = document.getElementById('pixi-container');
            
            try {
                // 清理旧应用
                if (app) {
                    app.destroy(true);
                }
                container.innerHTML = '';

                // 创建新应用
                app = new PIXI.Application({
                    width: 400,
                    height: 300,
                    backgroundColor: 0x333333,
                    antialias: true
                });

                container.appendChild(app.view);
                statusDiv.textContent = '✅ PIXI 应用创建成功';
                statusDiv.className = 'status success';
                
                console.log('PIXI 应用创建成功:', app);
                
            } catch (error) {
                statusDiv.textContent = `❌ PIXI 应用创建失败: ${error.message}`;
                statusDiv.className = 'status error';
                console.error('PIXI 应用创建失败:', error);
            }
        }

        // 绘制图形
        function drawGraphics() {
            const statusDiv = document.getElementById('graphics-status');
            
            if (!app) {
                statusDiv.textContent = '❌ 请先创建 PIXI 应用';
                statusDiv.className = 'status error';
                return;
            }

            try {
                // 清空舞台
                app.stage.removeChildren();

                const centerX = app.screen.width / 2;
                const centerY = app.screen.height / 2;

                // 绘制一个蓝色矩形
                const rect = new PIXI.Graphics();
                rect.beginFill(0x3498db);
                rect.drawRect(-30, -30, 60, 60);
                rect.x = centerX;
                rect.y = centerY;
                app.stage.addChild(rect);

                // 绘制红色圆形边框
                const circle1 = new PIXI.Graphics();
                circle1.lineStyle(2, 0xe74c3c, 1);
                circle1.drawCircle(0, 0, 50);
                circle1.x = centerX;
                circle1.y = centerY;
                app.stage.addChild(circle1);

                // 绘制橙色圆形边框
                const circle2 = new PIXI.Graphics();
                circle2.lineStyle(2, 0xf39c12, 1);
                circle2.drawCircle(0, 0, 80);
                circle2.x = centerX;
                circle2.y = centerY;
                app.stage.addChild(circle2);

                // 绘制紫色圆形边框
                const circle3 = new PIXI.Graphics();
                circle3.lineStyle(2, 0x9b59b6, 1);
                circle3.drawCircle(0, 0, 110);
                circle3.x = centerX;
                circle3.y = centerY;
                app.stage.addChild(circle3);

                // 绘制中心点
                const centerPoint = new PIXI.Graphics();
                centerPoint.beginFill(0x2c3e50);
                centerPoint.drawCircle(0, 0, 3);
                centerPoint.x = centerX;
                centerPoint.y = centerY;
                app.stage.addChild(centerPoint);

                statusDiv.textContent = '✅ 图形绘制成功';
                statusDiv.className = 'status success';
                
            } catch (error) {
                statusDiv.textContent = `❌ 图形绘制失败: ${error.message}`;
                statusDiv.className = 'status error';
                console.error('图形绘制失败:', error);
            }
        }

        // 测试范围预览
        function testRangePreview() {
            const statusDiv = document.getElementById('ranges-status');
            
            if (!app) {
                statusDiv.textContent = '❌ 请先创建 PIXI 应用';
                statusDiv.className = 'status error';
                return;
            }

            try {
                // 模拟实体数据
                const entityData = {
                    name: '测试实体',
                    defaultWidth: 60,
                    defaultHeight: 60,
                    collisionBox: { width: 40, height: 40 },
                    attackRange: { width: 80, height: 80 },
                    alertRange: { width: 120, height: 120 }
                };

                // 清空舞台
                app.stage.removeChildren();

                const centerX = app.screen.width / 2;
                const centerY = app.screen.height / 2;
                const scale = 1;

                // 先绘制实体
                const entitySprite = new PIXI.Graphics();
                entitySprite.beginFill(0x3498db);
                const width = entityData.defaultWidth * scale;
                const height = entityData.defaultHeight * scale;
                entitySprite.drawRect(-width/2, -height/2, width, height);
                entitySprite.x = centerX;
                entitySprite.y = centerY;
                app.stage.addChild(entitySprite);

                // 绘制警戒范围
                const alertRadius = Math.max(entityData.alertRange.width, entityData.alertRange.height) / 2 * scale;
                const alertCircle = new PIXI.Graphics();
                alertCircle.lineStyle(2, 0x9b59b6, 1);
                alertCircle.drawCircle(0, 0, alertRadius);
                alertCircle.x = centerX;
                alertCircle.y = centerY;
                app.stage.addChild(alertCircle);

                // 绘制攻击范围
                const attackRadius = Math.max(entityData.attackRange.width, entityData.attackRange.height) / 2 * scale;
                const attackCircle = new PIXI.Graphics();
                attackCircle.lineStyle(2, 0xf39c12, 1);
                attackCircle.drawCircle(0, 0, attackRadius);
                attackCircle.x = centerX;
                attackCircle.y = centerY;
                app.stage.addChild(attackCircle);

                // 绘制碰撞框
                const collisionRadius = Math.max(entityData.collisionBox.width, entityData.collisionBox.height) / 2 * scale;
                const collisionCircle = new PIXI.Graphics();
                collisionCircle.lineStyle(2, 0xe74c3c, 1);
                collisionCircle.drawCircle(0, 0, collisionRadius);
                collisionCircle.x = centerX;
                collisionCircle.y = centerY;
                app.stage.addChild(collisionCircle);

                // 绘制中心点
                const centerPoint = new PIXI.Graphics();
                centerPoint.beginFill(0x2c3e50);
                centerPoint.drawCircle(0, 0, 3);
                centerPoint.x = centerX;
                centerPoint.y = centerY;
                app.stage.addChild(centerPoint);

                statusDiv.textContent = '✅ 范围预览测试成功';
                statusDiv.className = 'status success';
                
            } catch (error) {
                statusDiv.textContent = `❌ 范围预览测试失败: ${error.message}`;
                statusDiv.className = 'status error';
                console.error('范围预览测试失败:', error);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 检测PIXI加载状态
            checkPixiLoaded();

            // 绑定事件
            document.getElementById('create-app-btn').addEventListener('click', createPixiApp);
            document.getElementById('draw-graphics-btn').addEventListener('click', drawGraphics);
            document.getElementById('test-ranges-btn').addEventListener('click', testRangePreview);
        });
    </script>
</body>
</html>
