# 实体范围预览功能说明

## 功能概述

在游戏管理后台中，现在可以为玩家和敌人设置碰撞框、攻击范围和警戒范围（仅敌人），并提供可视化预览功能。

## 新增功能

### 玩家编辑功能
- **碰撞框设置**：可以设置玩家的碰撞框宽度和高度
- **攻击范围设置**：可以设置玩家的攻击范围宽度和高度
- **预览功能**：点击"预览范围"按钮可以可视化查看各种范围

### 敌人编辑功能
- **碰撞框设置**：可以设置敌人的碰撞框宽度和高度
- **攻击范围设置**：可以设置敌人的攻击范围宽度和高度
- **警戒范围设置**：可以设置敌人的警戒范围宽度和高度
- **预览功能**：点击"预览范围"按钮可以可视化查看各种范围

## 使用方法

### 1. 编辑玩家
1. 在管理后台中点击"玩家管理"
2. 点击"创建新玩家"或编辑现有玩家
3. 在表单中找到"碰撞和范围设置"部分
4. 设置以下参数：
   - 碰撞框宽度/高度：用于物理碰撞检测
   - 攻击范围宽度/高度：用于攻击判定
5. 点击"预览范围"按钮查看可视化效果
6. 保存玩家数据

### 2. 编辑敌人
1. 在管理后台中点击"敌人管理"
2. 点击"创建新敌人"或编辑现有敌人
3. 在表单中找到"碰撞和范围设置"部分
4. 设置以下参数：
   - 碰撞框宽度/高度：用于物理碰撞检测
   - 攻击范围宽度/高度：用于攻击判定
   - 警戒范围宽度/高度：用于检测玩家进入警戒区域
5. 点击"预览范围"按钮查看可视化效果
6. 保存敌人数据

## 预览功能说明

### 预览窗口组成
- **标题栏**：显示实体名称和关闭按钮
- **画布区域**：显示各种范围的可视化图形
- **图例区域**：说明各种颜色代表的含义

### 显示说明
- **实体动画**：显示实际的待机动画（如果有配置），否则显示蓝色矩形（在底层）
- **红色圆形边框**：碰撞框范围（不可见填充，在上层）
- **橙色圆形边框**：攻击范围（不可见填充，在上层）
- **紫色圆形边框**：警戒范围（仅敌人，不可见填充，在上层）
- **黑色圆点**：实体中心点（在最上层）

### 新特性
- **真实动画显示**：如果实体配置了待机动画，预览中会显示实际的动画效果
- **圆形范围**：所有范围都使用圆形显示，更符合游戏中的实际判定
- **不可见范围**：碰撞框和攻击范围只显示边界，内部透明，更清晰地展示范围大小
- **分层显示**：范围显示在实体动画前面，确保范围边界清晰可见

### 实时预览
- 当预览窗口打开时，修改输入框中的数值会实时更新预览图形
- 图例中的数值也会同步更新

## 技术实现

### 数据结构
```javascript
// 玩家数据结构
{
  // ... 其他属性
  collisionBox: {
    width: 40,
    height: 40
  },
  attackRange: {
    width: 80,
    height: 80
  }
}

// 敌人数据结构
{
  // ... 其他属性
  collisionBox: {
    width: 30,
    height: 30
  },
  attackRange: {
    width: 60,
    height: 60
  },
  alertRange: {
    width: 120,
    height: 120
  }
}
```

### 主要函数
- `showEntityRangePreview(entityType)`：显示范围预览
- `createRangePreviewWindow(entityType, entityData)`：创建预览窗口
- `createPixiRangePreview(entityType, entityData)`：创建PIXI预览应用
- `loadEntityIdleAnimation(entityType, entityData, centerX, centerY, scale)`：加载实体待机动画
- `createSimpleEntitySprite(entityData, centerX, centerY, scale)`：创建简单实体图形
- `setupRealTimePreview(entityType)`：设置实时预览
- `updateRangePreview(entityType)`：更新预览内容

### 技术特点
- **PIXI.js渲染**：使用PIXI.js进行高性能图形渲染
- **动画支持**：支持显示实际的游戏动画
- **圆形范围**：使用圆形更准确地表示游戏中的范围判定
- **实时更新**：输入值变化时立即更新预览
- **错误处理**：包含完善的错误处理和回退机制

## 测试页面

项目中包含一个独立的测试页面 `test-ranges.html`，可以用来测试预览功能而无需完整的管理后台环境。

访问方式：`http://localhost:8000/test-ranges.html`

## 注意事项

1. 所有范围数值必须为正整数
2. 预览功能使用PIXI.js渲染，需要现代浏览器支持WebGL
3. 预览窗口会自动缩放以确保所有范围都能在画布中显示
4. 同时只能打开一个预览窗口
5. 修改输入值时，如果预览窗口已打开，会自动更新预览内容
6. 动画预览需要实体配置了待机动画，否则显示简单的蓝色矩形
7. 范围显示为圆形，半径取宽度和高度的最大值的一半
8. 如果PIXI.js加载失败，会自动回退到文本显示模式

## 默认值

### 玩家默认值
- 碰撞框：40x40
- 攻击范围：80x80

### 敌人默认值
- 碰撞框：30x30
- 攻击范围：60x60
- 警戒范围：120x120
