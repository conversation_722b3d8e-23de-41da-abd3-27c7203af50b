/**
 * 地图管理器类
 * 用于管理游戏地图的创建、编辑和加载
 */
class MapManager {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.maps = {};
        
        // 默认网格大小
        this.defaultGridSize = 80;
        
        // 默认地图尺寸
        this.defaultMapWidth = 16;
        this.defaultMapHeight = 12;
    }
    
    /**
     * 初始化地图管理器
     */
    async initialize() {
        try {
            // 加载所有地图数据
            const maps = await this.dbManager.getAllData(this.dbManager.stores.maps);
            maps.forEach(map => {
                this.maps[map.id] = map;
            });
            
            console.log('地图管理器初始化完成，加载了', maps.length, '个地图');
            return true;
        } catch (error) {
            console.error('地图管理器初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 创建新地图
     * @param {Object} mapData 地图数据
     * @returns {Promise<number>} 新地图的ID
     */
    async createMap(mapData) {
        try {
            // 确保地图数据包含必要的字段
            const newMap = {
                name: mapData.name || '新地图',
                date: new Date().toISOString(),
                gridSize: mapData.gridSize || this.defaultGridSize,
                width: mapData.width || this.defaultMapWidth,
                height: mapData.height || this.defaultMapHeight,
                backgroundImage: mapData.backgroundImage || null,
                walls: mapData.walls || this.createEmptyWallsData(mapData.width || this.defaultMapWidth, mapData.height || this.defaultMapHeight),
                enemies: mapData.enemies || [],
                playerStart: mapData.playerStart || { x: 1, y: 1 },
                paths: mapData.paths || []
            };
            
            // 添加到数据库
            const id = await this.dbManager.addData(this.dbManager.stores.maps, newMap);
            newMap.id = id;
            this.maps[id] = newMap;
            
            console.log('创建新地图成功，ID:', id);
            return id;
        } catch (error) {
            console.error('创建地图失败:', error);
            throw error;
        }
    }
    
    /**
     * 创建空的墙壁数据
     * @param {number} width 地图宽度
     * @param {number} height 地图高度
     * @returns {Array} 墙壁数据二维数组
     */
    createEmptyWallsData(width, height) {
        // 创建一个二维数组，边界为墙壁(1)，内部为空地(0)
        const walls = [];
        
        for (let y = 0; y < height; y++) {
            const row = [];
            for (let x = 0; x < width; x++) {
                // 如果是边界，设置为墙壁
                if (x === 0 || y === 0 || x === width - 1 || y === height - 1) {
                    row.push(1);
                } else {
                    row.push(0);
                }
            }
            walls.push(row);
        }
        
        return walls;
    }
    
    /**
     * 更新地图
     * @param {Object} mapData 地图数据
     * @returns {Promise<Object>} 更新后的地图数据
     */
    async updateMap(mapData) {
        try {
            // 确保有ID
            if (!mapData.id) {
                throw new Error('更新地图需要提供ID');
            }
            
            // 更新日期
            mapData.date = new Date().toISOString();
            
            // 更新数据库
            await this.dbManager.updateData(this.dbManager.stores.maps, mapData);
            this.maps[mapData.id] = mapData;
            
            console.log('更新地图成功，ID:', mapData.id);
            return mapData;
        } catch (error) {
            console.error('更新地图失败:', error);
            throw error;
        }
    }
    
    /**
     * 删除地图
     * @param {number} id 地图ID
     * @returns {Promise<boolean>} 是否删除成功
     */
    async deleteMap(id) {
        try {
            await this.dbManager.deleteData(this.dbManager.stores.maps, id);
            delete this.maps[id];
            
            console.log('删除地图成功，ID:', id);
            return true;
        } catch (error) {
            console.error('删除地图失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取地图
     * @param {number} id 地图ID
     * @returns {Object} 地图数据
     */
    getMap(id) {
        const map = this.maps[id];
        if (!map) {
            console.warn('未找到ID为', id, '的地图');
        }
        return map;
    }
    
    /**
     * 获取所有地图
     * @returns {Array} 地图数组
     */
    getAllMaps() {
        return Object.values(this.maps);
    }
}
