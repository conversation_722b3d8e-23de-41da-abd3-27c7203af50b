/**
 * 数据库修复脚本
 * 用于修复数据库版本升级问题
 */
document.addEventListener('DOMContentLoaded', async function() {
    // 删除旧的数据库
    try {
        console.log('尝试删除旧的数据库...');
        await deleteDatabase('AdventureGameDB');
        console.log('旧数据库删除成功');
        
        // 显示成功消息
        alert('数据库已重置。请刷新页面以创建新的数据库。');
        
        // 刷新页面
        window.location.reload();
    } catch (error) {
        console.error('删除数据库失败:', error);
        alert('删除数据库失败: ' + error.message);
    }
});

/**
 * 删除IndexedDB数据库
 * @param {string} dbName 数据库名称
 * @returns {Promise} 删除操作的Promise
 */
function deleteDatabase(dbName) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.deleteDatabase(dbName);
        
        request.onsuccess = () => {
            console.log(`数据库 ${dbName} 删除成功`);
            resolve();
        };
        
        request.onerror = (event) => {
            console.error(`删除数据库 ${dbName} 失败:`, event.target.error);
            reject(event.target.error);
        };
        
        request.onblocked = (event) => {
            console.warn(`数据库 ${dbName} 删除被阻塞，可能有连接未关闭`);
            // 尝试关闭所有连接
            const connections = indexedDB.databases();
            connections.then(dbs => {
                dbs.forEach(db => {
                    if (db.name === dbName) {
                        console.log(`尝试关闭数据库 ${dbName} 的连接`);
                        // 无法直接关闭，但可以提示用户
                    }
                });
            });
        };
    });
}
