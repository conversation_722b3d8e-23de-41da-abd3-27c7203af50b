/**
 * 主游戏类
 */
class Game {
    constructor() {
        // 获取容器尺寸
        const gameContainer = document.getElementById('gameContainer');
        const containerWidth = gameContainer.clientWidth;
        const containerHeight = gameContainer.clientHeight;

        // 设置游戏尺寸
        this.gameWidth = 1280;
        this.gameHeight = 900;

        // 创建PIXI应用
        this.app = new PIXI.Application({
            width: this.gameWidth,
            height: this.gameHeight,
            backgroundColor: 0x000000,
            antialias: true,
            resolution: window.devicePixelRatio || 1,
            autoDensity: true, // 自动调整DPI
            resizeTo: gameContainer // 自动调整大小以适应容器
        });

        // 添加到DOM
        gameContainer.appendChild(this.app.view);

        // 游戏状态
        this.gameState = {
            running: false,
            score: 0,
            health: 100
        };

        // 游戏组件
        this.gameMap = null;
        this.player = null;
        this.enemies = [];
        this.maxEnemies = 5; // 增加敌人数量以适应更大的地图

        // 游戏UI
        this.scoreText = null;
        this.healthText = null;

        // 数据管理器
        this.dbManager = new DatabaseManager();
        this.spriteManager = new SpriteManager(this.dbManager);
        this.entityManager = new EntityManager(this.dbManager, this.spriteManager);

        // 战斗系统
        this.collisionSystem = new CollisionSystem(this.app);
        this.combatSystem = new CombatSystem(this.app, this.collisionSystem);

        // 设置窗口大小调整事件
        window.addEventListener('resize', this.onResize.bind(this));

        // 初始化游戏
        this.initGame();
    }

    /**
     * 初始化游戏
     */
    async initGame() {
        try {
            console.log('开始初始化游戏数据库...');

            // 初始化数据库
            await this.dbManager.initDatabase();
            console.log('数据库初始化完成');

            await this.spriteManager.initialize();
            console.log('精灵管理器初始化完成');

            await this.entityManager.initialize();
            console.log('实体管理器初始化完成');

            // 加载游戏设置
            await this.loadGameSettings();
            console.log('游戏设置加载完成');

            // 初始化游戏
            await this.init();
            console.log('游戏初始化完成');

            // 添加调试按钮
            this.addDebugButton();

        } catch (error) {
            console.error('游戏初始化失败:', error);
            // 显示错误信息
            this.showErrorScreen('游戏初始化失败，请刷新页面重试');
        }
    }

    /**
     * 添加调试按钮
     */
    addDebugButton() {
        // 创建调试按钮
        const debugBtn = document.createElement('button');
        debugBtn.textContent = '重新加载实体';
        debugBtn.onclick = async () => {
            console.log('重新加载实体...');

            // 移除现有实体
            if (this.player && this.player.sprite) {
                this.app.stage.removeChild(this.player.sprite);
            }

            for (const enemy of this.enemies) {
                if (enemy.sprite) {
                    this.app.stage.removeChild(enemy.sprite);
                }
            }

            // 重新创建实体
            await this.createPlayer();
            await this.createEnemies();

            console.log('实体重新加载完成');
        };

        // 创建调试模式切换按钮
        const debugModeBtn = document.createElement('button');
        debugModeBtn.textContent = '切换调试模式';
        debugModeBtn.onclick = () => {
            if (this.collisionSystem) {
                const currentMode = this.collisionSystem.debugMode;
                this.collisionSystem.setDebugMode(!currentMode);
                console.log(`调试模式: ${!currentMode ? '开启' : '关闭'}`);
            }
        };

        // 添加到控制区域
        const controlsDiv = document.querySelector('.game-controls');
        if (controlsDiv) {
            controlsDiv.appendChild(debugBtn);
            controlsDiv.appendChild(debugModeBtn);
        }
    }

    /**
     * 加载游戏设置
     */
    async loadGameSettings() {
        try {
            const settings = await this.dbManager.getAllData(this.dbManager.stores.gameSettings);

            if (settings.length > 0) {
                const setting = settings[0];

                // 应用设置
                if (setting.width && setting.height) {
                    this.gameWidth = setting.width;
                    this.gameHeight = setting.height;

                    // 更新应用尺寸
                    this.app.renderer.resize(this.gameWidth, this.gameHeight);
                }

                if (setting.enemySpawnRate) {
                    this.enemySpawnRate = setting.enemySpawnRate;
                }

                // 保存默认玩家ID
                if (setting.defaultPlayer) {
                    this.defaultPlayerId = setting.defaultPlayer;
                }
            }

        } catch (error) {
            console.error('加载游戏设置失败:', error);
        }
    }

    // 窗口大小调整处理
    onResize() {
        // 游戏已经通过resizeTo自动调整大小
        // 但我们需要调整UI元素位置和地图位置
        if (this.scoreText) {
            this.scoreText.x = 30;
            this.scoreText.y = 30;
        }

        if (this.healthText) {
            this.healthText.x = 30;
            this.healthText.y = 70;
        }

        // 重新计算地图位置
        if (this.gameMap) {
            this.gameMap.calculateMapPosition();
        }
    }

    /**
     * 初始化游戏组件
     */
    async init() {
        try {
            // 检查URL参数是否有地图ID
            const urlParams = new URLSearchParams(window.location.search);
            const mapId = urlParams.get('map');

            // 创建游戏地图
            if (mapId) {
                // 如果有地图ID，从数据库加载地图
                console.log('从数据库加载地图，ID:', mapId);
                await this.loadMapFromDatabase(mapId);
            } else {
                // 否则使用默认地图
                this.gameMap = new GameMap(this.app);
            }

            // 创建玩家
            await this.createPlayer();

            // 创建敌人
            await this.createEnemies();

            // 创建UI
            this.createUI();

            // 设置游戏循环
            this.app.ticker.add(this.gameLoop.bind(this));

            // 开始游戏
            this.gameState.running = true;

        } catch (error) {
            console.error('游戏初始化失败:', error);
            this.showErrorScreen('游戏初始化失败: ' + error.message);
        }
    }

    /**
     * 从数据库加载地图
     * @param {number} mapId 地图ID
     */
    async loadMapFromDatabase(mapId) {
        try {
            // 获取地图数据
            const mapData = await this.dbManager.getData(this.dbManager.stores.maps, parseInt(mapId));

            if (!mapData) {
                throw new Error('未找到地图数据');
            }

            console.log('地图数据加载成功:', mapData);

            // 创建地图
            this.gameMap = new GameMap(this.app, mapData);

            // 如果地图有玩家起点，设置玩家位置
            if (mapData.playerStart) {
                this.playerStartPosition = {
                    x: mapData.playerStart.x * this.gameMap.tileSize + this.gameMap.offsetX,
                    y: mapData.playerStart.y * this.gameMap.tileSize + this.gameMap.offsetY
                };
            }

            // 如果地图有敌人，使用地图中的敌人
            if (mapData.enemies && mapData.enemies.length > 0) {
                this.mapEnemies = mapData.enemies;
            }

            return true;
        } catch (error) {
            console.error('加载地图失败:', error);
            throw error;
        }
    }

    /**
     * 显示错误屏幕
     * @param {string} message 错误信息
     */
    showErrorScreen(message) {
        // 创建错误文本
        const errorText = new PIXI.Text(message, {
            fontFamily: 'Arial',
            fontSize: 24,
            fill: 0xff0000,
            align: 'center'
        });

        errorText.anchor.set(0.5);
        errorText.x = this.app.screen.width / 2;
        errorText.y = this.app.screen.height / 2;

        this.app.stage.addChild(errorText);

        // 添加重试按钮
        const retryButton = new PIXI.Text('点击重试', {
            fontFamily: 'Arial',
            fontSize: 18,
            fill: 0xffffff,
            align: 'center'
        });

        retryButton.anchor.set(0.5);
        retryButton.x = this.app.screen.width / 2;
        retryButton.y = this.app.screen.height / 2 + 40;
        retryButton.interactive = true;
        retryButton.buttonMode = true;

        retryButton.on('pointerdown', () => {
            window.location.reload();
        });

        this.app.stage.addChild(retryButton);
    }

    /**
     * 创建玩家
     */
    async createPlayer() {
        try {
            console.log('开始创建玩家...');

            // 检查是否有默认玩家
            if (this.defaultPlayerId) {
                console.log(`使用默认玩家ID: ${this.defaultPlayerId}`);

                // 从数据库创建玩家实体
                const playerEntity = await this.entityManager.createGameEntity('player', this.defaultPlayerId);
                console.log('从数据库加载的玩家实体:', playerEntity);

                // 创建玩家
                this.player = new Player(this.app, this.gameMap, playerEntity);
                console.log('使用默认玩家创建完成');
            } else {
                // 获取所有玩家
                const players = await this.dbManager.getAllData(this.dbManager.stores.players);
                console.log(`数据库中找到 ${players.length} 个玩家`);

                if (players.length > 0) {
                    // 使用第一个玩家
                    console.log(`使用第一个玩家ID: ${players[0].id}, 名称: ${players[0].name}`);
                    const playerEntity = await this.entityManager.createGameEntity('player', players[0].id);
                    console.log('从数据库加载的玩家实体:', playerEntity);

                    this.player = new Player(this.app, this.gameMap, playerEntity);
                    console.log('使用数据库玩家创建完成');
                } else {
                    console.log('没有玩家数据，使用默认玩家');
                    // 没有玩家数据，使用默认玩家
                    this.player = new Player(this.app, this.gameMap);
                }
            }

            // 如果有自定义起点位置，设置玩家位置
            if (this.playerStartPosition) {
                this.player.x = this.playerStartPosition.x;
                this.player.y = this.playerStartPosition.y;
                console.log('设置玩家起点位置:', this.playerStartPosition);
            }

            // 设置战斗系统
            this.player.setCombatSystems(this.combatSystem, this.collisionSystem);
        } catch (error) {
            console.error('创建玩家失败:', error);
            // 使用默认玩家
            this.player = new Player(this.app, this.gameMap);
        }
    }

    async loadResources() {
        // 加载游戏资源
        // 使用已有的图片资源
        try {
            // 直接使用Texture.from加载纹理，这是一种更简单的方法
            const playerTexture = PIXI.Texture.from('./tupian/wanlinyi.png');
            const enemyTexture = PIXI.Texture.from('./tupian/wanlindai.png');

            // 确保纹理已加载完成
            await new Promise(resolve => {
                // 如果纹理已加载，直接解析
                if (playerTexture.valid && enemyTexture.valid) {
                    resolve();
                } else {
                    // 否则等待纹理加载完成
                    const textureLoadCheck = () => {
                        if (playerTexture.valid && enemyTexture.valid) {
                            resolve();
                        } else {
                            requestAnimationFrame(textureLoadCheck);
                        }
                    };
                    textureLoadCheck();
                }
            });

            // 设置玩家纹理
            if (this.player) {
                // 直接使用已加载的纹理
                this.player.setTexture(playerTexture);
            }

            // 设置敌人纹理
            if (this.enemies.length > 0) {
                for (const enemy of this.enemies) {
                    // 直接使用已加载的纹理
                    enemy.setTexture(enemyTexture);
                }
            }
        } catch (error) {
            console.error("加载资源时出错:", error);
            // 即使加载失败，也继续使用默认图形
        }
    }

    /**
     * 创建敌人
     */
    async createEnemies() {
        try {
            console.log('开始创建敌人...');

            // 清空现有敌人
            for (const enemy of this.enemies) {
                if (enemy.sprite) {
                    this.app.stage.removeChild(enemy.sprite);
                }

                // 清除动画精灵
                if (enemy.currentAnimation) {
                    this.app.stage.removeChild(enemy.currentAnimation);
                }

                // 清除所有动画精灵
                for (const type in enemy.animatedSprites) {
                    for (const direction in enemy.animatedSprites[type]) {
                        const sprite = enemy.animatedSprites[type][direction];
                        if (sprite) {
                            this.app.stage.removeChild(sprite);
                        }
                    }
                }
            }
            this.enemies = [];

            // 如果地图中有敌人生成点，使用它们
            if (this.mapEnemies && this.mapEnemies.length > 0) {
                console.log(`地图中找到 ${this.mapEnemies.length} 个敌人生成点`);

                // 获取所有敌人类型
                const enemyTypes = await this.dbManager.getAllData(this.dbManager.stores.enemies);

                if (enemyTypes.length === 0) {
                    console.log('没有敌人数据，使用默认敌人');
                    this.createDefaultEnemies();
                    return;
                }

                // 为每个生成点创建敌人
                for (const spawnPoint of this.mapEnemies) {
                    try {
                        // 获取敌人类型
                        const enemyId = spawnPoint.id;

                        // 计算位置（从网格坐标转换为像素坐标）
                        const x = spawnPoint.x * this.gameMap.tileSize + this.gameMap.offsetX;
                        const y = spawnPoint.y * this.gameMap.tileSize + this.gameMap.offsetY;

                        console.log(`在位置 (${x}, ${y}) 创建敌人，ID: ${enemyId}`);

                        // 从数据库创建敌人实体
                        const enemyEntity = await this.entityManager.createGameEntity('enemy', enemyId);

                        // 设置位置
                        enemyEntity.x = x;
                        enemyEntity.y = y;

                        // 创建敌人
                        const enemy = new Enemy(this.app, this.gameMap, enemyEntity);
                        enemy.setCombatSystems(this.combatSystem, this.collisionSystem);
                        this.enemies.push(enemy);
                    } catch (error) {
                        console.error(`创建地图敌人失败:`, error);
                        // 使用默认敌人
                        const x = spawnPoint.x * this.gameMap.tileSize + this.gameMap.offsetX;
                        const y = spawnPoint.y * this.gameMap.tileSize + this.gameMap.offsetY;
                        const enemy = new Enemy(this.app, this.gameMap, x, y);
                        enemy.setCombatSystems(this.combatSystem, this.collisionSystem);
                        this.enemies.push(enemy);
                    }
                }

                console.log(`从地图创建了 ${this.enemies.length} 个敌人`);
                return;
            }

            // 获取所有敌人类型
            const enemyTypes = await this.dbManager.getAllData(this.dbManager.stores.enemies);
            console.log(`数据库中找到 ${enemyTypes.length} 种敌人类型`);

            if (enemyTypes.length === 0) {
                console.log('没有敌人数据，使用默认敌人');
                // 没有敌人数据，使用默认敌人
                this.createDefaultEnemies();
                return;
            }

            // 创建敌人
            for (let i = 0; i < this.maxEnemies; i++) {
                // 随机选择一个敌人类型
                const enemyType = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                console.log(`创建第 ${i+1} 个敌人，类型: ${enemyType.name}, ID: ${enemyType.id}`);

                // 随机位置，但避开玩家初始位置
                let x, y;
                let safeDistance = 150; // 与玩家的安全距离
                let isValidPosition = false;

                // 尝试找到一个有效的位置
                while (!isValidPosition) {
                    // 在地图范围内随机生成位置
                    x = Math.floor(Math.random() * (this.app.screen.width - 200)) + 100;
                    y = Math.floor(Math.random() * (this.app.screen.height - 200)) + 100;

                    // 确保敌人不会生成在墙壁上
                    const enemyBounds = {
                        x: x,
                        y: y,
                        width: enemyType.width || 50,
                        height: enemyType.height || 50
                    };

                    // 检查位置是否有效（不靠近玩家且不在墙上）
                    const tooCloseToPlayer = this.player &&
                                           Math.abs(x - this.player.x) < safeDistance &&
                                           Math.abs(y - this.player.y) < safeDistance;
                    const collidesWithWall = this.gameMap.checkCollision(enemyBounds);

                    isValidPosition = !tooCloseToPlayer && !collidesWithWall;
                }

                try {
                    // 从数据库创建敌人实体
                    const enemyEntity = await this.entityManager.createGameEntity('enemy', enemyType.id);
                    console.log('从数据库加载的敌人实体:', enemyEntity);

                    // 设置位置
                    enemyEntity.x = x;
                    enemyEntity.y = y;

                    // 创建敌人
                    const enemy = new Enemy(this.app, this.gameMap, enemyEntity);
                    enemy.setCombatSystems(this.combatSystem, this.collisionSystem);
                    this.enemies.push(enemy);
                    console.log(`敌人 ${i+1} 创建成功，位置: (${x}, ${y})`);
                } catch (error) {
                    console.error(`创建敌人 ${i+1} 实体失败:`, error);
                    // 使用默认敌人
                    const enemy = new Enemy(this.app, this.gameMap, x, y);
                    enemy.setCombatSystems(this.combatSystem, this.collisionSystem);
                    this.enemies.push(enemy);
                    console.log(`使用默认敌人 ${i+1} 创建完成，位置: (${x}, ${y})`);
                }
            }

            console.log(`共创建了 ${this.enemies.length} 个敌人`);
        } catch (error) {
            console.error('创建敌人失败:', error);
            // 使用默认敌人
            this.createDefaultEnemies();
        }
    }

    /**
     * 创建默认敌人（当数据库中没有敌人数据时使用）
     */
    createDefaultEnemies() {
        // 创建敌人
        for (let i = 0; i < this.maxEnemies; i++) {
            // 随机位置，但避开玩家初始位置
            let x, y;
            let safeDistance = 150; // 与玩家的安全距离
            let isValidPosition = false;

            // 尝试找到一个有效的位置
            while (!isValidPosition) {
                // 在地图范围内随机生成位置
                x = Math.floor(Math.random() * (this.app.screen.width - 200)) + 100;
                y = Math.floor(Math.random() * (this.app.screen.height - 200)) + 100;

                // 确保敌人不会生成在墙壁上
                const enemyBounds = {
                    x: x,
                    y: y,
                    width: 50,
                    height: 50
                };

                // 检查位置是否有效（不靠近玩家且不在墙上）
                const tooCloseToPlayer = this.player &&
                                       Math.abs(x - this.player.x) < safeDistance &&
                                       Math.abs(y - this.player.y) < safeDistance;
                const collidesWithWall = this.gameMap.checkCollision(enemyBounds);

                isValidPosition = !tooCloseToPlayer && !collidesWithWall;
            }

            const enemy = new Enemy(this.app, this.gameMap, x, y);
            enemy.setCombatSystems(this.combatSystem, this.collisionSystem);
            this.enemies.push(enemy);
        }
    }

    createUI() {
        // 创建分数文本
        this.scoreText = new PIXI.Text('分数: 0', {
            fontFamily: 'Arial',
            fontSize: 28, // 增大字体
            fill: 0xffffff,
            fontWeight: 'bold',
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowDistance: 2
        });
        this.scoreText.x = 30;
        this.scoreText.y = 30;
        this.app.stage.addChild(this.scoreText);

        // 创建生命值文本
        this.healthText = new PIXI.Text('生命值: 100', {
            fontFamily: 'Arial',
            fontSize: 28, // 增大字体
            fill: 0xffffff,
            fontWeight: 'bold',
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowDistance: 2
        });
        this.healthText.x = 30;
        this.healthText.y = 70;
        this.app.stage.addChild(this.healthText);
    }

    gameLoop(delta) {
        if (!this.gameState.running) return;

        // 清除调试图形
        if (this.collisionSystem) {
            this.collisionSystem.clearDebugGraphics();
        }

        // 更新玩家
        this.player.update();

        // 更新敌人
        for (const enemy of this.enemies) {
            enemy.update();
        }

        // 检查玩家是否死亡
        if (this.combatSystem && this.combatSystem.isDead(this.player)) {
            this.gameOver();
        }

        // 更新UI
        this.updateUI();
    }

    handleEnemyCollision() {
        // 减少生命值
        this.gameState.health -= 1;

        // 检查游戏是否结束
        if (this.gameState.health <= 0) {
            this.gameOver();
        }
    }

    updateUI() {
        // 更新分数和生命值显示
        this.scoreText.text = `分数: ${this.gameState.score}`;

        // 显示玩家的实际生命值
        const playerHealth = this.player ? this.player.health : this.gameState.health;
        const playerMaxHealth = this.player ? this.player.maxHealth : 100;
        this.healthText.text = `生命值: ${playerHealth}/${playerMaxHealth}`;
    }

    gameOver() {
        // 游戏结束
        this.gameState.running = false;

        // 创建半透明背景
        const overlay = new PIXI.Graphics();
        overlay.beginFill(0x000000, 0.7);
        overlay.drawRect(0, 0, this.app.screen.width, this.app.screen.height);
        overlay.endFill();
        this.app.stage.addChild(overlay);

        // 显示游戏结束文本
        const gameOverText = new PIXI.Text('游戏结束', {
            fontFamily: 'Arial',
            fontSize: 64,
            fontWeight: 'bold',
            fill: 0xff0000,
            align: 'center',
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowDistance: 4
        });
        gameOverText.anchor.set(0.5);
        gameOverText.x = this.app.screen.width / 2;
        gameOverText.y = this.app.screen.height / 2 - 40;
        this.app.stage.addChild(gameOverText);

        // 显示分数
        const finalScoreText = new PIXI.Text(`最终分数: ${this.gameState.score}`, {
            fontFamily: 'Arial',
            fontSize: 36,
            fill: 0xffffff,
            align: 'center',
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowDistance: 2
        });
        finalScoreText.anchor.set(0.5);
        finalScoreText.x = this.app.screen.width / 2;
        finalScoreText.y = this.app.screen.height / 2 + 30;
        this.app.stage.addChild(finalScoreText);

        // 创建重新开始按钮
        const restartButton = new PIXI.Graphics();
        restartButton.beginFill(0x3498db);
        restartButton.lineStyle(4, 0x2980b9);
        restartButton.drawRoundedRect(0, 0, 200, 60, 15);
        restartButton.endFill();
        restartButton.x = this.app.screen.width / 2 - 100;
        restartButton.y = this.app.screen.height / 2 + 100;
        restartButton.interactive = true;
        restartButton.buttonMode = true;
        restartButton.on('pointerdown', () => this.restart());
        this.app.stage.addChild(restartButton);

        // 按钮文本
        const restartText = new PIXI.Text('重新开始', {
            fontFamily: 'Arial',
            fontSize: 28,
            fontWeight: 'bold',
            fill: 0xffffff,
            align: 'center'
        });
        restartText.anchor.set(0.5);
        restartText.x = restartButton.x + 100;
        restartText.y = restartButton.y + 30;
        this.app.stage.addChild(restartText);
    }

    restart() {
        // 重置游戏状态
        this.gameState.running = true;
        this.gameState.score = 0;
        this.gameState.health = 100;

        // 移除所有敌人
        for (const enemy of this.enemies) {
            this.app.stage.removeChild(enemy.sprite);
        }
        this.enemies = [];

        // 重置玩家位置到屏幕中心
        this.player.x = this.app.screen.width / 2 - this.player.width / 2;
        this.player.y = this.app.screen.height / 2 - this.player.height / 2;

        // 重新创建敌人
        this.createEnemies();

        // 移除游戏结束界面的所有元素
        for (let i = this.app.stage.children.length - 1; i >= 0; i--) {
            const child = this.app.stage.children[i];
            // 移除半透明背景、游戏结束文本、分数文本、重新开始按钮及其文本
            if (child instanceof PIXI.Graphics &&
                (child.width === this.app.screen.width || child.width === 200)) {
                this.app.stage.removeChild(child);
            }
            if (child instanceof PIXI.Text &&
                (child.text === '游戏结束' ||
                 child.text.includes('最终分数') ||
                 child.text === '重新开始')) {
                this.app.stage.removeChild(child);
            }
        }

        // 更新UI
        this.updateUI();
    }
}

// 当页面加载完成后启动游戏
window.onload = () => {
    const game = new Game();
};
