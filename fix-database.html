<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复数据库</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        p {
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: #fff;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 16px;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-danger {
            background-color: #e74c3c;
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .actions {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据库修复工具</h1>
        
        <p>此页面用于修复数据库版本升级问题。点击下面的按钮将删除现有数据库并创建新的数据库。</p>
        
        <div class="warning">
            <p><strong>警告：</strong>此操作将删除所有现有数据，包括玩家、敌人、精灵表、动画和地图数据。此操作不可撤销。</p>
        </div>
        
        <div class="actions">
            <button id="fix-db-btn" class="btn btn-danger">删除数据库并重新创建</button>
            <a href="admin.html" class="btn">返回管理后台</a>
        </div>
    </div>
    
    <script src="js/fix-database.js"></script>
</body>
</html>
