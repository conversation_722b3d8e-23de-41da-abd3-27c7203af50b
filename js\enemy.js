/**
 * 敌人类
 */
class Enemy {
    /**
     * 创建敌人
     * @param {PIXI.Application} app PIXI应用
     * @param {GameMap} gameMap 游戏地图
     * @param {Object|number} entityOrX 敌人实体数据或X坐标
     * @param {number} y Y坐标（当第三个参数为X坐标时使用）
     */
    constructor(app, gameMap, entityOrX, y) {
        this.app = app;
        this.gameMap = gameMap;
        this.sprite = null;
        this.animatedSprites = {};
        this.currentAnimation = null;
        this.direction = 'down'; // 默认朝下

        // 战斗系统引用（将在游戏初始化时设置）
        this.combatSystem = null;
        this.collisionSystem = null;

        // AI状态
        this.aiState = 'patrol'; // patrol, alert, chase, attack, dead
        this.lastStateChange = Date.now();
        this.stateChangeDelay = 1000; // 状态改变的最小间隔

        // 检查第三个参数是否为实体对象
        if (typeof entityOrX === 'object' && entityOrX !== null) {
            // 使用实体数据
            const entity = entityOrX;
            this.name = entity.name;
            this.health = entity.health;
            this.maxHealth = entity.maxHealth;
            this.attack = entity.attack;
            this.defense = entity.defense;
            this.speed = entity.speed;
            // 移除整体的宽度和高度
            // this.width = entity.width;
            // this.height = entity.height;
            this.animations = entity.animations;
            // 动画尺寸配置，包含每个动画的宽度和高度
            this.animationSizes = entity.animationSizes || {};
            // 默认动画尺寸
            this.defaultAnimationSize = {
                width: entity.defaultAnimationWidth || 50,
                height: entity.defaultAnimationHeight || 50
            };
            this.x = entity.x;
            this.y = entity.y;
        } else {
            // 使用默认值和坐标
            this.name = "敌人";
            this.health = 50;
            this.maxHealth = 50;
            this.attack = 5;
            this.defense = 2;
            this.speed = 2;
            // 移除整体的宽度和高度
            // this.width = 50;
            // this.height = 50;
            this.animations = {};
            // 动画尺寸配置，包含每个动画的宽度和高度
            this.animationSizes = {};
            // 默认动画尺寸
            this.defaultAnimationSize = {
                width: 50,
                height: 50
            };

            // 如果没有提供位置，则随机生成位置
            this.x = entityOrX || (Math.random() * (this.app.screen.width - 200) + 100);
            this.y = y || (Math.random() * (this.app.screen.height - 200) + 100);
        }

        // 路径跟随相关属性
        this.currentPath = null;     // 当前跟随的路径
        this.currentPathIndex = 0;   // 当前路径点索引
        this.pathDirection = 1;      // 路径跟随方向: 1=正向, -1=反向
        this.followPathEnabled = true; // 是否启用路径跟随

        // 移动相关属性
        this.moveDirection = Math.floor(Math.random() * 4); // 0: 上, 1: 右, 2: 下, 3: 左
        this.moveCounter = 0;
        this.moveInterval = 120; // 每隔多少帧改变方向

        // 选择一个路径
        this.selectPath();

        this.init();
    }

    /**
     * 设置战斗系统
     * @param {CombatSystem} combatSystem 战斗系统
     * @param {CollisionSystem} collisionSystem 碰撞系统
     */
    setCombatSystems(combatSystem, collisionSystem) {
        this.combatSystem = combatSystem;
        this.collisionSystem = collisionSystem;

        // 初始化战斗属性
        if (this.combatSystem) {
            this.combatSystem.initEntityCombat(this);
        }
    }

    /**
     * 初始化敌人
     */
    init() {
        // 检查是否有动画
        if (this.animations && Object.keys(this.animations).length > 0) {
            // 使用默认精灵作为占位符，直到动画加载完成
            this.createDefaultSprite();

            // 加载动画
            this.loadAnimations();
        } else {
            // 没有动画，使用默认精灵
            this.createDefaultSprite();
        }
    }

    /**
     * 创建默认精灵
     */
    createDefaultSprite() {
        // 创建一个简单的图形作为敌人精灵
        this.sprite = new PIXI.Container();

        // 创建背景
        const background = new PIXI.Graphics();
        background.beginFill(0xe74c3c, 0.7);
        // 使用默认动画尺寸
        background.drawRect(0, 0, this.defaultAnimationSize.width, this.defaultAnimationSize.height);
        background.endFill();

        // 创建文本"E"
        const text = new PIXI.Text('E', {
            fontFamily: 'Arial',
            fontSize: this.defaultAnimationSize.width * 0.6,
            fill: 0xFFFFFF,
            align: 'center',
            fontWeight: 'bold'
        });

        // 居中文本
        text.anchor.set(0.5);
        text.x = this.defaultAnimationSize.width / 2;
        text.y = this.defaultAnimationSize.height / 2;

        // 添加到容器
        this.sprite.addChild(background);
        this.sprite.addChild(text);

        // 设置位置
        this.sprite.x = this.x;
        this.sprite.y = this.y;

        // 添加到舞台
        this.app.stage.addChild(this.sprite);
    }

    /**
     * 加载动画
     */
    async loadAnimations() {
        // 等待游戏实例初始化完成
        let attempts = 0;
        const maxAttempts = 10;

        while (!window.game || !window.game.spriteManager) {
            if (attempts >= maxAttempts) {
                console.warn('无法加载敌人动画：找不到精灵管理器');
                return;
            }

            console.log('等待精灵管理器初始化...');
            await new Promise(resolve => setTimeout(resolve, 500));
            attempts++;
        }

        console.log('精灵管理器已找到，开始加载敌人动画');
        const spriteManager = window.game.spriteManager;

        // 加载所有动画
        for (const [type, animationId] of Object.entries(this.animations)) {
            if (!animationId) continue;

            try {
                // 为每个方向创建动画精灵
                const directions = ['down', 'up', 'left', 'right'];

                for (const direction of directions) {
                    try {
                        const animatedSprite = await spriteManager.createAnimatedSprite(animationId, direction);

                        if (!this.animatedSprites[type]) {
                            this.animatedSprites[type] = {};
                        }

                        this.animatedSprites[type][direction] = animatedSprite;

                        // 设置位置
                        animatedSprite.x = this.x;
                        animatedSprite.y = this.y;

                        // 获取该动画类型的尺寸配置，如果没有则使用默认
                        const size = this.animationSizes[type] || this.defaultAnimationSize;
                        animatedSprite.width = size.width;
                        animatedSprite.height = size.height;

                        // 默认隐藏
                        animatedSprite.visible = false;

                        // 添加到舞台
                        this.app.stage.addChild(animatedSprite);
                    } catch (error) {
                        console.warn(`无法创建${direction}方向的${type}动画:`, error);
                    }
                }

                console.log(`敌人${type}动画加载成功`);
            } catch (error) {
                console.error(`加载敌人${type}动画失败:`, error);
            }
        }

        // 设置默认动画
        this.setAnimation('idle');
    }

    /**
     * 选择一个路径跟随
     */
    selectPath() {
        // 检查地图是否有路径
        if (this.gameMap && this.gameMap.paths && this.gameMap.paths.length > 0) {
            // 随机选择一个路径
            const pathIndex = Math.floor(Math.random() * this.gameMap.paths.length);
            this.currentPath = this.gameMap.paths[pathIndex];

            // 随机选择开始的路径点索引和方向
            if (Math.random() > 0.5) {
                this.currentPathIndex = 0;
                this.pathDirection = 1; // 正向
            } else {
                this.currentPathIndex = this.currentPath.points.length - 1;
                this.pathDirection = -1; // 反向
            }

            // 使用固定的基准尺寸 - 与地图渲染中使用的基准尺寸保持一致
            const baseSize = 80;

            // 将敌人放置在路径的起始点
            const startPoint = this.currentPath.points[this.currentPathIndex];
            this.x = (startPoint.x + 0.5) * baseSize + this.gameMap.container.x / this.gameMap.container.scale.x;
            this.y = (startPoint.y + 0.5) * baseSize + this.gameMap.container.y / this.gameMap.container.scale.y;
        }
    }

    /**
     * 沿路径移动
     */
    moveAlongPath() {
        // 如果没有路径或路径点不足，返回
        if (!this.currentPath || !this.currentPath.points || this.currentPath.points.length < 2) {
            return false;
        }

        // 获取当前目标点
        const targetPoint = this.currentPath.points[this.currentPathIndex];

        // 使用固定的基准尺寸 - 与地图渲染中使用的基准尺寸保持一致
        const baseSize = 80;

        // 计算目标坐标（考虑地图偏移和缩放）
        const targetX = (targetPoint.x + 0.5) * baseSize + this.gameMap.container.x / this.gameMap.container.scale.x;
        const targetY = (targetPoint.y + 0.5) * baseSize + this.gameMap.container.y / this.gameMap.container.scale.y;

        // 计算到目标的距离
        const dx = targetX - this.x;
        const dy = targetY - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 如果已到达目标点附近，移动到下一个点
        if (distance < this.speed) {
            // 更新到下一个路径点
            this.currentPathIndex += this.pathDirection;

            // 如果到达路径端点，反转方向
            if (this.currentPathIndex >= this.currentPath.points.length || this.currentPathIndex < 0) {
                this.pathDirection *= -1;
                this.currentPathIndex += this.pathDirection * 2; // 跳过当前点
            }

            return true;
        }

        // 向目标点移动
        const moveDistance = Math.min(this.speed, distance);
        const angle = Math.atan2(dy, dx);

        this.x += Math.cos(angle) * moveDistance;
        this.y += Math.sin(angle) * moveDistance;

        // 更新朝向
        if (Math.abs(dx) > Math.abs(dy)) {
            // 水平移动为主
            this.setDirection(dx > 0 ? 'right' : 'left');
        } else {
            // 垂直移动为主
            this.setDirection(dy > 0 ? 'down' : 'up');
        }

        return true;
    }

    /**
     * 设置动画
     * @param {string} type 动画类型
     */
    setAnimation(type) {
        // 如果没有动画，直接返回
        if (!this.animatedSprites[type] || !this.animatedSprites[type][this.direction]) {
            return;
        }

        // 停止当前动画
        if (this.currentAnimation) {
            this.currentAnimation.visible = false;
            this.currentAnimation.stop();
        }

        // 隐藏默认精灵
        if (this.sprite) {
            this.sprite.visible = false;
        }

        // 设置新动画
        this.currentAnimation = this.animatedSprites[type][this.direction];
        this.currentAnimation.visible = true;
        this.currentAnimation.play();
    }

    /**
     * 设置方向
     * @param {string} direction 方向
     */
    setDirection(direction) {
        // 如果方向相同，不做任何改变
        if (this.direction === direction) {
            return;
        }

        this.direction = direction;

        // 更新当前动画
        if (this.currentAnimation) {
            const currentType = Object.keys(this.animatedSprites).find(type =>
                this.animatedSprites[type][this.direction] === this.currentAnimation
            );

            if (currentType && this.animatedSprites[currentType][direction]) {
                // 停止当前动画
                this.currentAnimation.visible = false;
                this.currentAnimation.stop();

                // 设置新方向的动画
                this.currentAnimation = this.animatedSprites[currentType][direction];
                this.currentAnimation.visible = true;
                this.currentAnimation.play();
            }
        }
    }

    /**
     * 更新敌人状态
     */
    update() {
        // 如果敌人已死亡，不进行任何更新
        if (this.combatSystem && this.combatSystem.isDead(this)) {
            this.aiState = 'dead';
            return;
        }

        // 获取玩家引用
        const player = window.game ? window.game.player : null;
        if (!player) {
            return;
        }

        // 保存当前位置用于碰撞检测
        const prevX = this.x;
        const prevY = this.y;

        // 记录是否移动
        let isMoving = false;

        // AI状态机
        this.updateAI(player);

        // 根据AI状态执行行为
        switch (this.aiState) {
            case 'patrol':
                isMoving = this.patrolBehavior();
                break;
            case 'alert':
                isMoving = this.alertBehavior(player);
                break;
            case 'chase':
                isMoving = this.chaseBehavior(player);
                break;
            case 'attack':
                isMoving = this.attackBehavior(player);
                break;
            case 'dead':
                return; // 死亡状态不做任何事
        }

        // 检查墙壁碰撞
        if (this.collisionSystem) {
            if (this.collisionSystem.checkWallCollision(this, this.gameMap)) {
                // 如果发生碰撞，恢复之前的位置
                this.x = prevX;
                this.y = prevY;
                isMoving = false;

                // 如果在巡逻状态，改变移动方向
                if (this.aiState === 'patrol') {
                    this.moveDirection = (this.moveDirection + 2) % 4; // 反向
                }
            }
        } else {
            // 回退到原来的碰撞检测
            let currentSize = this.defaultAnimationSize;

            if (this.currentAnimation) {
                const currentType = Object.keys(this.animatedSprites).find(type =>
                    Object.values(this.animatedSprites[type]).includes(this.currentAnimation)
                );

                if (currentType) {
                    currentSize = this.animationSizes[currentType] || this.defaultAnimationSize;
                }
            }

            const enemyBounds = {
                x: this.x,
                y: this.y,
                width: currentSize.width,
                height: currentSize.height
            };

            if (this.gameMap.checkCollision(enemyBounds)) {
                this.x = prevX;
                this.y = prevY;
                isMoving = false;

                if (this.aiState === 'patrol') {
                    this.moveDirection = (this.moveDirection + 2) % 4; // 反向
                }
            }
        }

        // 更新动画（只有在空闲状态时才更新移动/待机动画）
        const combatState = this.combatSystem ? this.combatSystem.getCombatState(this) : 'idle';
        if (combatState === 'idle') {
            if (isMoving) {
                // 如果有移动动画，使用移动动画
                if (this.animatedSprites.move && this.animatedSprites.move[this.direction]) {
                    this.setAnimation('move');
                }
            } else {
                // 如果有待机动画，使用待机动画
                if (this.animatedSprites.idle && this.animatedSprites.idle[this.direction]) {
                    this.setAnimation('idle');
                }
            }
        }

        // 更新精灵位置
        if (this.sprite) {
            this.sprite.x = this.x;
            this.sprite.y = this.y;
        }

        // 更新动画精灵位置
        if (this.currentAnimation) {
            this.currentAnimation.x = this.x;
            this.currentAnimation.y = this.y;
        }
    }

    // 检查与玩家的碰撞
    checkPlayerCollision(player) {
        // 获取当前动画的尺寸，或使用默认尺寸
        let currentSize = this.defaultAnimationSize;

        if (this.currentAnimation) {
            const currentType = Object.keys(this.animatedSprites).find(type =>
                Object.values(this.animatedSprites[type]).includes(this.currentAnimation)
            );

            if (currentType) {
                currentSize = this.animationSizes[currentType] || this.defaultAnimationSize;
            }
        }

        const enemyBounds = {
            x: this.x,
            y: this.y,
            width: currentSize.width,
            height: currentSize.height
        };

        // 获取玩家当前动画的尺寸
        let playerSize = { width: 60, height: 60 }; // 默认尺寸

        if (player.currentAnimation) {
            const playerType = Object.keys(player.animatedSprites).find(type =>
                Object.values(player.animatedSprites[type]).includes(player.currentAnimation)
            );

            if (playerType && player.animationSizes && player.animationSizes[playerType]) {
                playerSize = player.animationSizes[playerType];
            } else if (player.defaultAnimationSize) {
                playerSize = player.defaultAnimationSize;
            }
        }

        const playerBounds = {
            x: player.x,
            y: player.y,
            width: playerSize.width,
            height: playerSize.height
        };

        return this.isColliding(enemyBounds, playerBounds);
    }

    // 简单的AABB碰撞检测
    isColliding(a, b) {
        return a.x < b.x + b.width &&
               a.x + a.width > b.x &&
               a.y < b.y + b.height &&
               a.y + a.height > b.y;
    }

    // 设置敌人纹理
    setTexture(texture) {
        // 移除旧的精灵
        if (this.sprite) {
            this.app.stage.removeChild(this.sprite);
        }

        // 创建新的精灵
        this.sprite = new PIXI.Sprite(texture);
        // 使用默认动画尺寸
        this.sprite.width = this.defaultAnimationSize.width;
        this.sprite.height = this.defaultAnimationSize.height;
        this.sprite.x = this.x;
        this.sprite.y = this.y;

        // 添加到舞台
        this.app.stage.addChild(this.sprite);
    }

    // 保留旧方法以兼容性
    loadTexture(texturePath) {
        try {
            const texture = PIXI.Texture.from(texturePath);
            this.setTexture(texture);
        } catch (error) {
            console.error("加载敌人纹理失败:", error);
        }
    }

    /**
     * 更新AI状态
     * @param {Object} player 玩家对象
     */
    updateAI(player) {
        if (!this.collisionSystem || !this.combatSystem) {
            return;
        }

        const now = Date.now();

        // 防止状态变化过于频繁
        if (now - this.lastStateChange < this.stateChangeDelay) {
            return;
        }

        // 检查玩家是否在警戒范围内
        const inAlertRange = this.collisionSystem.checkInAlertRange(this, player);

        // 检查玩家是否在攻击范围内
        const inAttackRange = this.collisionSystem.checkAttackHit(this, player);

        // 状态转换逻辑
        switch (this.aiState) {
            case 'patrol':
                if (inAttackRange) {
                    this.changeState('attack');
                } else if (inAlertRange) {
                    this.changeState('alert');
                }
                break;

            case 'alert':
                if (inAttackRange) {
                    this.changeState('attack');
                } else if (inAlertRange) {
                    this.changeState('chase');
                } else {
                    this.changeState('patrol');
                }
                break;

            case 'chase':
                if (inAttackRange) {
                    this.changeState('attack');
                } else if (!inAlertRange) {
                    this.changeState('patrol');
                }
                break;

            case 'attack':
                if (!inAttackRange) {
                    if (inAlertRange) {
                        this.changeState('chase');
                    } else {
                        this.changeState('patrol');
                    }
                }
                break;
        }
    }

    /**
     * 改变AI状态
     * @param {string} newState 新状态
     */
    changeState(newState) {
        if (this.aiState !== newState) {
            this.aiState = newState;
            this.lastStateChange = Date.now();
            console.log(`敌人 ${this.name} 状态改变为: ${newState}`);
        }
    }

    /**
     * 巡逻行为
     * @returns {boolean} 是否移动
     */
    patrolBehavior() {
        // 尝试沿路径移动
        if (this.followPathEnabled && this.currentPath) {
            return this.moveAlongPath();
        }

        // 如果没有路径，使用随机移动
        return this.randomMove();
    }

    /**
     * 警戒行为
     * @param {Object} player 玩家对象
     * @returns {boolean} 是否移动
     */
    alertBehavior(player) {
        // 面向玩家
        this.facePlayer(player);

        // 警戒状态下不移动，只是观察
        return false;
    }

    /**
     * 追击行为
     * @param {Object} player 玩家对象
     * @returns {boolean} 是否移动
     */
    chaseBehavior(player) {
        return this.moveTowardsPlayer(player);
    }

    /**
     * 攻击行为
     * @param {Object} player 玩家对象
     * @returns {boolean} 是否移动
     */
    attackBehavior(player) {
        // 面向玩家
        this.facePlayer(player);

        // 尝试攻击玩家
        if (this.combatSystem.canAttack(this)) {
            // 随机选择普通攻击或技能攻击
            if (Math.random() < 0.3 && this.combatSystem.canUseSkill(this)) {
                this.combatSystem.performSkillAttack(this, player);
            } else {
                this.combatSystem.performAttack(this, player);
            }
        }

        // 攻击时不移动
        return false;
    }

    /**
     * 随机移动
     * @returns {boolean} 是否移动
     */
    randomMove() {
        // 移动计数器
        this.moveCounter++;
        if (this.moveCounter >= this.moveInterval) {
            this.moveCounter = 0;
            this.moveDirection = Math.floor(Math.random() * 4);
        }

        let newDirection = this.direction;
        let isMoving = false;

        // 根据方向移动
        switch (this.moveDirection) {
            case 0: // 上
                this.y -= this.speed;
                newDirection = 'up';
                isMoving = true;
                break;
            case 1: // 右
                this.x += this.speed;
                newDirection = 'right';
                isMoving = true;
                break;
            case 2: // 下
                this.y += this.speed;
                newDirection = 'down';
                isMoving = true;
                break;
            case 3: // 左
                this.x -= this.speed;
                newDirection = 'left';
                isMoving = true;
                break;
        }

        // 更新方向
        if (newDirection !== this.direction) {
            this.setDirection(newDirection);
        }

        return isMoving;
    }

    /**
     * 向玩家移动
     * @param {Object} player 玩家对象
     * @returns {boolean} 是否移动
     */
    moveTowardsPlayer(player) {
        if (!this.collisionSystem) {
            return false;
        }

        const direction = this.collisionSystem.getDirection(this, player);

        // 移动向玩家
        this.x += direction.x * this.speed;
        this.y += direction.y * this.speed;

        // 更新朝向
        if (Math.abs(direction.x) > Math.abs(direction.y)) {
            this.setDirection(direction.x > 0 ? 'right' : 'left');
        } else {
            this.setDirection(direction.y > 0 ? 'down' : 'up');
        }

        return true;
    }

    /**
     * 面向玩家
     * @param {Object} player 玩家对象
     */
    facePlayer(player) {
        if (!this.collisionSystem) {
            return;
        }

        const direction = this.collisionSystem.getDirection(this, player);

        // 更新朝向
        if (Math.abs(direction.x) > Math.abs(direction.y)) {
            this.setDirection(direction.x > 0 ? 'right' : 'left');
        } else {
            this.setDirection(direction.y > 0 ? 'down' : 'up');
        }
    }
}
