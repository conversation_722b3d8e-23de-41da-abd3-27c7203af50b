* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #333;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    overflow: hidden;
}

#gameContainer {
    position: relative;
    width: 100%;
    max-width: 1280px;
    height: 90vh;
    max-height: 900px;
    background-color: #000;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* 确保游戏画布适应容器 */
canvas {
    display: block;
    width: 100%;
    height: 100%;
}

/* 游戏控制按钮 */
.game-controls {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    width: 100%;
    max-width: 1280px;
}

.game-controls button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    margin: 0 5px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.game-controls button:hover {
    background-color: #2980b9;
}
