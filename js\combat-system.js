/**
 * 战斗系统
 * 处理攻击、技能、伤害计算、生命值管理等战斗相关功能
 */
class CombatSystem {
    constructor(app, collisionSystem) {
        this.app = app;
        this.collisionSystem = collisionSystem;
        this.damageNumbers = [];

        // 攻击冷却时间（毫秒）
        this.attackCooldown = 1000; // 1秒
        this.skillCooldown = 3000;  // 3秒

        // 动画持续时间（毫秒）
        this.attackAnimationDuration = 500;
        this.skillAnimationDuration = 800;
        this.deathAnimationDuration = 1000;
        this.reviveAnimationDuration = 1000;
    }

    /**
     * 初始化实体的战斗属性
     * @param {Object} entity 实体对象
     */
    initEntityCombat(entity) {
        if (!entity.combat) {
            entity.combat = {
                lastAttackTime: 0,
                lastSkillTime: 0,
                isAttacking: false,
                isUsingSkill: false,
                isDead: false,
                isReviving: false,
                currentState: 'idle' // idle, attacking, skill, dead, reviving
            };
        }
    }

    /**
     * 检查是否可以攻击
     * @param {Object} entity 实体对象
     * @returns {boolean} 是否可以攻击
     */
    canAttack(entity) {
        this.initEntityCombat(entity);
        const now = Date.now();
        return !entity.combat.isDead &&
               !entity.combat.isAttacking &&
               !entity.combat.isUsingSkill &&
               !entity.combat.isReviving &&
               (now - entity.combat.lastAttackTime) >= this.attackCooldown;
    }

    /**
     * 检查是否可以使用技能
     * @param {Object} entity 实体对象
     * @returns {boolean} 是否可以使用技能
     */
    canUseSkill(entity) {
        this.initEntityCombat(entity);
        const now = Date.now();
        return !entity.combat.isDead &&
               !entity.combat.isAttacking &&
               !entity.combat.isUsingSkill &&
               !entity.combat.isReviving &&
               (now - entity.combat.lastSkillTime) >= this.skillCooldown;
    }

    /**
     * 执行攻击
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 攻击是否成功
     */
    performAttack(attacker, target) {
        if (!this.canAttack(attacker)) {
            return false;
        }

        // 检查攻击范围
        if (!this.collisionSystem.checkAttackHit(attacker, target)) {
            return false;
        }

        this.initEntityCombat(attacker);
        this.initEntityCombat(target);

        // 如果目标已死亡，不能攻击
        if (target.combat.isDead) {
            return false;
        }

        // 立即设置攻击状态，阻止移动
        attacker.combat.isAttacking = true;
        attacker.combat.currentState = 'attacking';
        attacker.combat.lastAttackTime = Date.now();

        // 播放攻击动画
        if (attacker.setAnimation) {
            attacker.setAnimation('attack');
            // 立即强制更新动画位置
            this.forceUpdateAnimationPosition(attacker);
        }

        // 计算伤害
        const damage = this.calculateDamage(attacker.attack || 10, target.defense || 0);

        // 应用伤害
        this.applyDamage(target, damage);

        // 显示伤害数字
        this.showDamageNumber(target, damage);

        // 攻击动画结束后恢复状态
        setTimeout(() => {
            if (attacker.combat && !attacker.combat.isDead) {
                attacker.combat.isAttacking = false;
                attacker.combat.currentState = 'idle';
                if (attacker.setAnimation) {
                    attacker.setAnimation('idle');
                    // 立即强制更新动画位置
                    this.forceUpdateAnimationPosition(attacker);
                }
            }
        }, this.attackAnimationDuration);

        return true;
    }

    /**
     * 执行技能攻击
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 技能攻击是否成功
     */
    performSkillAttack(attacker, target) {
        if (!this.canUseSkill(attacker)) {
            return false;
        }

        // 检查攻击范围（技能攻击使用相同的攻击范围）
        if (!this.collisionSystem.checkAttackHit(attacker, target)) {
            return false;
        }

        this.initEntityCombat(attacker);
        this.initEntityCombat(target);

        // 如果目标已死亡，不能攻击
        if (target.combat.isDead) {
            return false;
        }

        // 立即设置技能攻击状态，阻止移动
        attacker.combat.isUsingSkill = true;
        attacker.combat.currentState = 'skill';
        attacker.combat.lastSkillTime = Date.now();

        // 播放技能攻击动画
        if (attacker.setAnimation) {
            attacker.setAnimation('skill');
            // 立即强制更新动画位置
            this.forceUpdateAnimationPosition(attacker);
        }

        // 计算技能伤害（技能伤害是普通攻击的1.5倍）
        const baseDamage = this.calculateDamage(attacker.attack || 10, target.defense || 0);
        const skillDamage = Math.floor(baseDamage * 1.5);

        // 应用伤害
        this.applyDamage(target, skillDamage);

        // 显示伤害数字（技能伤害用不同颜色）
        this.showDamageNumber(target, skillDamage, 0xff8800);

        // 技能动画结束后恢复状态
        setTimeout(() => {
            if (attacker.combat && !attacker.combat.isDead) {
                attacker.combat.isUsingSkill = false;
                attacker.combat.currentState = 'idle';
                if (attacker.setAnimation) {
                    attacker.setAnimation('idle');
                    // 立即强制更新动画位置
                    this.forceUpdateAnimationPosition(attacker);
                }
            }
        }, this.skillAnimationDuration);

        return true;
    }

    /**
     * 计算伤害
     * @param {number} attack 攻击力
     * @param {number} defense 防御力
     * @returns {number} 实际伤害
     */
    calculateDamage(attack, defense) {
        const baseDamage = Math.max(1, attack - defense);
        // 添加一些随机性（±20%）
        const randomFactor = 0.8 + Math.random() * 0.4;
        return Math.floor(baseDamage * randomFactor);
    }

    /**
     * 应用伤害
     * @param {Object} target 目标实体
     * @param {number} damage 伤害值
     */
    applyDamage(target, damage) {
        this.initEntityCombat(target);

        target.health = Math.max(0, target.health - damage);

        // 检查是否死亡
        if (target.health <= 0 && !target.combat.isDead) {
            this.killEntity(target);
        }
    }

    /**
     * 杀死实体
     * @param {Object} entity 实体对象
     */
    killEntity(entity) {
        this.initEntityCombat(entity);

        entity.combat.isDead = true;
        entity.combat.currentState = 'dead';
        entity.health = 0;

        // 播放死亡动画
        if (entity.setAnimation) {
            entity.setAnimation('death');
            // 立即强制更新动画位置
            this.forceUpdateAnimationPosition(entity);
        }

        // 死亡动画结束后停止动画
        setTimeout(() => {
            if (entity.combat && entity.combat.isDead && entity.currentAnimation) {
                entity.currentAnimation.stop();
                // 设置为死亡动画的最后一帧
                if (entity.currentAnimation.totalFrames > 0) {
                    entity.currentAnimation.gotoAndStop(entity.currentAnimation.totalFrames - 1);
                }
            }
        }, this.deathAnimationDuration);

        console.log(`${entity.name || '实体'} 已死亡`);
    }

    /**
     * 复活实体
     * @param {Object} entity 实体对象
     * @param {number} healthPercent 复活时的生命值百分比（0-1）
     */
    reviveEntity(entity, healthPercent = 1.0) {
        this.initEntityCombat(entity);

        if (!entity.combat.isDead) {
            return false;
        }

        entity.combat.isReviving = true;
        entity.combat.currentState = 'reviving';

        // 播放复活动画
        if (entity.setAnimation) {
            entity.setAnimation('revive');
            // 立即强制更新动画位置
            this.forceUpdateAnimationPosition(entity);
        }

        // 复活动画结束后恢复状态
        setTimeout(() => {
            if (entity.combat) {
                entity.combat.isDead = false;
                entity.combat.isReviving = false;
                entity.combat.isAttacking = false;
                entity.combat.isUsingSkill = false;
                entity.combat.currentState = 'idle';
                entity.health = Math.floor((entity.maxHealth || 100) * healthPercent);

                if (entity.setAnimation) {
                    entity.setAnimation('idle');
                    // 立即强制更新动画位置
                    this.forceUpdateAnimationPosition(entity);
                }

                console.log(`${entity.name || '实体'} 已复活`);
            }
        }, this.reviveAnimationDuration);

        return true;
    }

    /**
     * 显示伤害数字
     * @param {Object} target 目标实体
     * @param {number} damage 伤害值
     * @param {number} color 颜色
     */
    showDamageNumber(target, damage, color = 0xff0000) {
        const damageText = new PIXI.Text(`-${damage}`, {
            fontFamily: 'Arial',
            fontSize: 24,
            fill: color,
            fontWeight: 'bold',
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowDistance: 2
        });

        // 设置位置在目标上方
        damageText.x = target.x + (target.defaultAnimationSize?.width || 60) / 2;
        damageText.y = target.y - 20;
        damageText.anchor.set(0.5);

        this.app.stage.addChild(damageText);

        // 动画效果：向上移动并淡出
        const startY = damageText.y;
        const startTime = Date.now();
        const duration = 1500;

        const animateDamage = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress >= 1) {
                this.app.stage.removeChild(damageText);
                return;
            }

            damageText.y = startY - progress * 50;
            damageText.alpha = 1 - progress;

            requestAnimationFrame(animateDamage);
        };

        animateDamage();
    }

    /**
     * 检查实体是否死亡
     * @param {Object} entity 实体对象
     * @returns {boolean} 是否死亡
     */
    isDead(entity) {
        this.initEntityCombat(entity);
        return entity.combat.isDead;
    }

    /**
     * 获取实体当前战斗状态
     * @param {Object} entity 实体对象
     * @returns {string} 当前状态
     */
    getCombatState(entity) {
        this.initEntityCombat(entity);
        return entity.combat.currentState;
    }

    /**
     * 强制更新实体动画位置
     * @param {Object} entity 实体对象
     */
    forceUpdateAnimationPosition(entity) {
        // 更新当前动画精灵位置
        if (entity.currentAnimation) {
            entity.currentAnimation.x = entity.x;
            entity.currentAnimation.y = entity.y;
        }

        // 更新所有动画精灵位置
        if (entity.animatedSprites) {
            for (const type in entity.animatedSprites) {
                for (const direction in entity.animatedSprites[type]) {
                    const sprite = entity.animatedSprites[type][direction];
                    if (sprite) {
                        sprite.x = entity.x;
                        sprite.y = entity.y;
                    }
                }
            }
        }
    }
}
