/**
 * 战斗系统
 * 处理攻击、技能、伤害计算、生命值管理等战斗相关功能
 */
class CombatSystem {
    constructor(app, collisionSystem) {
        this.app = app;
        this.collisionSystem = collisionSystem;
        this.damageNumbers = [];

        // 攻击冷却时间（毫秒）
        this.attackCooldown = 1000; // 1秒
        this.skillCooldown = 3000;  // 3秒

        // 动画持续时间（毫秒）- 增加时间确保动画完整播放
        this.attackAnimationDuration = 1200;  // 增加到1.2秒
        this.skillAnimationDuration = 1500;   // 增加到1.5秒
        this.deathAnimationDuration = 1500;   // 增加到1.5秒
        this.reviveAnimationDuration = 1500;  // 增加到1.5秒
    }

    /**
     * 初始化实体的战斗属性
     * @param {Object} entity 实体对象
     */
    initEntityCombat(entity) {
        if (!entity.combat) {
            entity.combat = {
                lastAttackTime: 0,
                lastSkillTime: 0,
                isAttacking: false,
                isUsingSkill: false,
                isDead: false,
                isReviving: false,
                currentState: 'idle' // idle, attacking, skill, dead, reviving
            };
        }
    }

    /**
     * 检查是否可以攻击
     * @param {Object} entity 实体对象
     * @returns {boolean} 是否可以攻击
     */
    canAttack(entity) {
        this.initEntityCombat(entity);
        const now = Date.now();
        return !entity.combat.isDead &&
               !entity.combat.isAttacking &&
               !entity.combat.isUsingSkill &&
               !entity.combat.isReviving &&
               (now - entity.combat.lastAttackTime) >= this.attackCooldown;
    }

    /**
     * 检查是否可以使用技能
     * @param {Object} entity 实体对象
     * @returns {boolean} 是否可以使用技能
     */
    canUseSkill(entity) {
        this.initEntityCombat(entity);
        const now = Date.now();
        return !entity.combat.isDead &&
               !entity.combat.isAttacking &&
               !entity.combat.isUsingSkill &&
               !entity.combat.isReviving &&
               (now - entity.combat.lastSkillTime) >= this.skillCooldown;
    }

    /**
     * 执行攻击
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 攻击是否成功
     */
    performAttack(attacker, target) {
        if (!this.canAttack(attacker)) {
            return false;
        }

        // 检查攻击范围
        if (!this.collisionSystem.checkAttackHit(attacker, target)) {
            return false;
        }

        this.initEntityCombat(attacker);
        this.initEntityCombat(target);

        // 如果目标已死亡，不能攻击
        if (target.combat.isDead) {
            return false;
        }

        // 设置攻击状态
        attacker.combat.isAttacking = true;
        attacker.combat.currentState = 'attacking';
        attacker.combat.lastAttackTime = Date.now();

        // 播放攻击动画
        if (attacker.setAnimation) {
            attacker.setAnimation('attack');
        }

        // 计算伤害
        const damage = this.calculateDamage(attacker.attack || 10, target.defense || 0);

        // 应用伤害
        this.applyDamage(target, damage);

        // 显示伤害数字
        this.showDamageNumber(target, damage);

        // 攻击动画结束后恢复状态
        setTimeout(() => {
            if (attacker.combat && !attacker.combat.isDead) {
                attacker.combat.isAttacking = false;
                attacker.combat.currentState = 'idle';
                if (attacker.setAnimation) {
                    attacker.setAnimation('idle');
                }
            }
        }, this.attackAnimationDuration);

        return true;
    }

    /**
     * 执行技能攻击
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     * @returns {boolean} 技能攻击是否成功
     */
    performSkillAttack(attacker, target) {
        if (!this.canUseSkill(attacker)) {
            return false;
        }

        // 检查技能攻击范围（技能攻击使用更大的范围）
        if (!this.collisionSystem.checkSkillHit(attacker, target)) {
            return false;
        }

        this.initEntityCombat(attacker);
        this.initEntityCombat(target);

        // 如果目标已死亡，不能攻击
        if (target.combat.isDead) {
            return false;
        }

        // 设置技能攻击状态
        attacker.combat.isUsingSkill = true;
        attacker.combat.currentState = 'skill';
        attacker.combat.lastSkillTime = Date.now();

        // 播放技能攻击动画
        if (attacker.setAnimation) {
            attacker.setAnimation('skill');
        }

        // 计算技能伤害（技能伤害是普通攻击的3倍，分两段）
        const baseDamage = this.calculateDamage(attacker.attack || 10, target.defense || 0);
        const firstHitDamage = Math.floor(baseDamage * 1.5);  // 第一段150%
        const secondHitDamage = Math.floor(baseDamage * 1.5); // 第二段150%

        // 应用第一段伤害
        this.applyDamage(target, firstHitDamage);
        this.showDamageNumber(target, firstHitDamage, 0xff8800);

        // 延迟应用第二段伤害
        setTimeout(() => {
            if (!this.isDead(target)) {
                this.applyDamage(target, secondHitDamage);
                this.showDamageNumber(target, secondHitDamage, 0xff4400);
            }
        }, 300); // 300毫秒后第二段伤害

        // 技能动画结束后恢复状态
        setTimeout(() => {
            if (attacker.combat && !attacker.combat.isDead) {
                attacker.combat.isUsingSkill = false;
                attacker.combat.currentState = 'idle';
                if (attacker.setAnimation) {
                    attacker.setAnimation('idle');
                }
            }
        }, this.skillAnimationDuration);

        return true;
    }

    /**
     * 计算伤害
     * @param {number} attack 攻击力
     * @param {number} defense 防御力
     * @returns {number} 实际伤害
     */
    calculateDamage(attack, defense) {
        const baseDamage = Math.max(1, attack - defense);
        // 添加一些随机性（±20%）
        const randomFactor = 0.8 + Math.random() * 0.4;
        return Math.floor(baseDamage * randomFactor);
    }

    /**
     * 应用伤害
     * @param {Object} target 目标实体
     * @param {number} damage 伤害值
     */
    applyDamage(target, damage) {
        this.initEntityCombat(target);

        target.health = Math.max(0, target.health - damage);

        // 检查是否死亡
        if (target.health <= 0 && !target.combat.isDead) {
            this.killEntity(target);
        }
    }

    /**
     * 杀死实体
     * @param {Object} entity 实体对象
     */
    killEntity(entity) {
        this.initEntityCombat(entity);

        entity.combat.isDead = true;
        entity.combat.currentState = 'dead';
        entity.health = 0;

        // 播放死亡动画
        if (entity.setAnimation) {
            entity.setAnimation('death');
        }

        // 死亡动画结束后暂停在最后一帧
        setTimeout(() => {
            if (entity.combat && entity.combat.isDead) {
                // 停止当前动画并暂停在最后一帧
                if (entity.currentAnimation) {
                    entity.currentAnimation.stop();
                    // 设置为死亡动画的最后一帧并保持显示
                    if (entity.currentAnimation.totalFrames > 0) {
                        entity.currentAnimation.gotoAndStop(entity.currentAnimation.totalFrames - 1);
                    }
                    // 保持动画可见，暂停在最后一帧
                    entity.currentAnimation.visible = true;
                }

                // 隐藏默认精灵（如果有的话）
                if (entity.sprite) {
                    entity.sprite.visible = false;
                }

                console.log(`${entity.name || '实体'} 死亡动画结束，暂停在最后一帧`);
            }
        }, this.deathAnimationDuration);

        console.log(`${entity.name || '实体'} 已死亡`);

        // 如果是敌人，设置自动复活
        if (entity.type === 'enemy' && entity.autoRevive !== false) {
            console.log(`检测到敌人死亡，准备安排复活: ${entity.name}, 类型: ${entity.type}`);
            this.scheduleAutoRevive(entity);
        } else {
            console.log(`不安排复活 - 类型: ${entity.type}, autoRevive: ${entity.autoRevive}`);
        }
    }

    /**
     * 复活实体
     * @param {Object} entity 实体对象
     * @param {number} healthPercent 复活时的生命值百分比（0-1）
     */
    reviveEntity(entity, healthPercent = 1.0) {
        this.initEntityCombat(entity);

        if (!entity.combat.isDead) {
            return false;
        }

        entity.combat.isReviving = true;
        entity.combat.currentState = 'reviving';

        // 播放复活动画
        if (entity.setAnimation) {
            entity.setAnimation('revive');
        }

        // 复活动画结束后恢复状态
        setTimeout(() => {
            if (entity.combat) {
                entity.combat.isDead = false;
                entity.combat.isReviving = false;
                entity.combat.isAttacking = false;
                entity.combat.isUsingSkill = false;
                entity.combat.currentState = 'idle';
                entity.health = Math.floor((entity.maxHealth || 100) * healthPercent);

                if (entity.setAnimation) {
                    entity.setAnimation('idle');
                }

                console.log(`${entity.name || '实体'} 已复活`);
            }
        }, this.reviveAnimationDuration);

        return true;
    }

    /**
     * 显示伤害数字
     * @param {Object} target 目标实体
     * @param {number} damage 伤害值
     * @param {number} color 颜色
     */
    showDamageNumber(target, damage, color = 0xff0000) {
        const damageText = new PIXI.Text(`-${damage}`, {
            fontFamily: 'Arial',
            fontSize: 24,
            fill: color,
            fontWeight: 'bold',
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowDistance: 2
        });

        // 设置位置在目标上方
        damageText.x = target.x + (target.defaultAnimationSize?.width || 60) / 2;
        damageText.y = target.y - 20;
        damageText.anchor.set(0.5);

        this.app.stage.addChild(damageText);

        // 动画效果：向上移动并淡出
        const startY = damageText.y;
        const startTime = Date.now();
        const duration = 1500;

        const animateDamage = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress >= 1) {
                this.app.stage.removeChild(damageText);
                return;
            }

            damageText.y = startY - progress * 50;
            damageText.alpha = 1 - progress;

            requestAnimationFrame(animateDamage);
        };

        animateDamage();
    }

    /**
     * 检查实体是否死亡
     * @param {Object} entity 实体对象
     * @returns {boolean} 是否死亡
     */
    isDead(entity) {
        this.initEntityCombat(entity);
        return entity.combat.isDead;
    }

    /**
     * 获取实体当前战斗状态
     * @param {Object} entity 实体对象
     * @returns {string} 当前状态
     */
    getCombatState(entity) {
        this.initEntityCombat(entity);
        return entity.combat.currentState;
    }

    /**
     * 计算动画持续时间
     * @param {Object} entity 实体对象
     * @param {string} animationType 动画类型
     * @returns {number} 动画持续时间（毫秒）
     */
    calculateAnimationDuration(entity, animationType) {
        try {
            // 尝试获取实际的动画精灵
            if (entity.animatedSprites &&
                entity.animatedSprites[animationType] &&
                entity.animatedSprites[animationType][entity.direction]) {

                const animSprite = entity.animatedSprites[animationType][entity.direction];

                // 计算基于帧数和播放速度的持续时间
                if (animSprite.totalFrames && animSprite.animationSpeed) {
                    const duration = (animSprite.totalFrames / animSprite.animationSpeed) * (1000 / 60); // 转换为毫秒
                    console.log(`${animationType}动画计算持续时间: ${duration}ms (帧数: ${animSprite.totalFrames}, 速度: ${animSprite.animationSpeed})`);
                    return Math.max(duration, 800); // 最少800ms
                }
            }
        } catch (error) {
            console.warn('计算动画持续时间失败:', error);
        }

        // 回退到默认时间
        switch (animationType) {
            case 'attack': return this.attackAnimationDuration;
            case 'skill': return this.skillAnimationDuration;
            case 'death': return this.deathAnimationDuration;
            case 'revive': return this.reviveAnimationDuration;
            default: return 1000;
        }
    }

    /**
     * 开始攻击动画（不检查目标，总是播放动画）
     * @param {Object} attacker 攻击者
     */
    startAttackAnimation(attacker) {
        this.initEntityCombat(attacker);

        // 设置攻击状态
        attacker.combat.isAttacking = true;
        attacker.combat.currentState = 'attacking';
        attacker.combat.lastAttackTime = Date.now();

        // 播放攻击动画
        if (attacker.setAnimation) {
            attacker.setAnimation('attack');
        }

        // 尝试监听动画完成事件
        this.setupAnimationCompleteListener(attacker, 'attack', () => {
            if (attacker.combat && !attacker.combat.isDead) {
                attacker.combat.isAttacking = false;
                attacker.combat.currentState = 'idle';
                if (attacker.setAnimation) {
                    attacker.setAnimation('idle');
                }
                console.log('攻击动画完成，切换到idle');
            }
        });
    }

    /**
     * 开始技能攻击动画（不检查目标，总是播放动画）
     * @param {Object} attacker 攻击者
     */
    startSkillAnimation(attacker) {
        this.initEntityCombat(attacker);

        // 设置技能攻击状态
        attacker.combat.isUsingSkill = true;
        attacker.combat.currentState = 'skill';
        attacker.combat.lastSkillTime = Date.now();

        // 播放技能攻击动画
        if (attacker.setAnimation) {
            attacker.setAnimation('skill');
        }

        // 尝试监听动画完成事件
        this.setupAnimationCompleteListener(attacker, 'skill', () => {
            if (attacker.combat && !attacker.combat.isDead) {
                attacker.combat.isUsingSkill = false;
                attacker.combat.currentState = 'idle';
                if (attacker.setAnimation) {
                    attacker.setAnimation('idle');
                }
                console.log('技能动画完成，切换到idle');
            }
        });
    }

    /**
     * 设置动画完成监听器
     * @param {Object} entity 实体对象
     * @param {string} animationType 动画类型
     * @param {Function} callback 完成回调
     */
    setupAnimationCompleteListener(entity, animationType, callback) {
        try {
            // 尝试获取当前动画精灵
            if (entity.currentAnimation && entity.currentAnimation.onComplete) {
                // 如果动画支持完成事件，使用事件
                entity.currentAnimation.onComplete = callback;
                console.log(`设置${animationType}动画完成监听器`);
                return;
            }
        } catch (error) {
            console.warn('设置动画完成监听器失败:', error);
        }

        // 回退到计时器方法
        const duration = this.calculateAnimationDuration(entity, animationType);
        console.log(`使用计时器方法，${animationType}动画持续时间: ${duration}ms`);

        setTimeout(callback, duration);
    }

    /**
     * 执行两段技能伤害（专为玩家技能攻击设计）
     * @param {Object} attacker 攻击者
     * @param {Object} target 目标
     */
    performTwoStageSkillDamage(attacker, target) {
        if (this.isDead(target)) {
            return;
        }

        // 计算基础伤害
        const baseDamage = this.calculateDamage(attacker.attack || 10, target.defense || 0);

        // 第一段伤害：150%
        const firstHitDamage = Math.floor(baseDamage * 1.5);
        this.applyDamage(target, firstHitDamage);
        this.showDamageNumber(target, firstHitDamage, 0xff8800);
        console.log(`技能第一段伤害: ${firstHitDamage}`);

        // 延迟第二段伤害：150%
        setTimeout(() => {
            if (!this.isDead(target)) {
                const secondHitDamage = Math.floor(baseDamage * 1.5);
                this.applyDamage(target, secondHitDamage);
                this.showDamageNumber(target, secondHitDamage, 0xff4400);
                console.log(`技能第二段伤害: ${secondHitDamage}`);
                console.log(`技能总伤害: ${firstHitDamage + secondHitDamage} (基础伤害的${((firstHitDamage + secondHitDamage) / baseDamage * 100).toFixed(0)}%)`);
            }
        }, 400); // 400毫秒后第二段伤害
    }

    /**
     * 安排敌人自动复活
     * @param {Object} entity 敌人实体
     */
    scheduleAutoRevive(entity) {
        // 设置复活时间（30秒）
        const reviveDelay = entity.reviveTime || 30000; // 30秒复活时间

        console.log(`${entity.name || '敌人'} 将在 ${reviveDelay / 1000} 秒后复活`);

        // 清除之前的复活定时器（如果存在）
        if (entity.reviveTimer) {
            clearTimeout(entity.reviveTimer);
        }

        // 设置复活定时器
        entity.reviveTimer = setTimeout(() => {
            console.log(`复活定时器触发: ${entity.name || '敌人'}`);
            if (entity.combat && entity.combat.isDead) {
                console.log(`开始执行复活: ${entity.name || '敌人'}`);
                this.autoReviveEnemy(entity);
            } else {
                console.log(`复活条件不满足: isDead=${entity.combat?.isDead}, combat=${!!entity.combat}`);
            }
        }, reviveDelay);

        console.log(`复活定时器已设置，ID: ${entity.reviveTimer}`);
    }

    /**
     * 安排玩家接近触发复活
     * @param {Object} entity 敌人实体
     */
    schedulePlayerTriggeredRevive(entity) {
        console.log(`${entity.name || '敌人'} 设置为玩家接近触发复活模式`);

        // 标记为等待玩家触发复活
        entity.waitingForPlayerTrigger = true;

        // 清除自动复活定时器（如果存在）
        if (entity.reviveTimer) {
            clearTimeout(entity.reviveTimer);
            entity.reviveTimer = null;
        }
    }

    /**
     * 检查玩家是否接近死亡的敌人，如果是则触发复活
     * @param {Object} entity 敌人实体
     * @param {Object} player 玩家对象
     */
    checkPlayerTriggeredRevive(entity, player) {
        if (!entity.waitingForPlayerTrigger || !entity.combat || !entity.combat.isDead) {
            return;
        }

        // 检查玩家是否在警戒范围内
        if (this.collisionSystem && this.collisionSystem.checkInAlertRange(entity, player)) {
            console.log(`玩家接近 ${entity.name || '敌人'}，触发复活`);
            entity.waitingForPlayerTrigger = false;
            this.autoReviveEnemy(entity);
        }
    }

    /**
     * 自动复活敌人
     * @param {Object} entity 敌人实体
     */
    autoReviveEnemy(entity) {
        if (!entity.combat || !entity.combat.isDead) {
            console.log(`复活失败: 实体状态不正确 - combat: ${!!entity.combat}, isDead: ${entity.combat?.isDead}`);
            return;
        }

        console.log(`${entity.name || '敌人'} 开始复活...`);

        // 完全重置战斗状态
        entity.combat.isDead = false;
        entity.combat.isReviving = true;
        entity.combat.currentState = 'reviving';
        entity.combat.isAttacking = false;
        entity.combat.isUsingSkill = false;

        // 恢复生命值到满血
        entity.health = entity.maxHealth || 100;
        console.log(`${entity.name || '敌人'} 生命值恢复到: ${entity.health}/${entity.maxHealth}`);

        // 重置AI状态
        entity.aiState = 'patrol';
        console.log(`${entity.name || '敌人'} AI状态重置为: patrol`);

        // 重新显示实体
        if (entity.sprite) {
            entity.sprite.visible = true;
            console.log(`${entity.name || '敌人'} 默认精灵已显示`);
        }
        if (entity.currentAnimation) {
            entity.currentAnimation.visible = true;
            console.log(`${entity.name || '敌人'} 当前动画已显示`);
        }

        // 播放复活动画（确保只播放一次）
        if (entity.setAnimation) {
            console.log(`${entity.name || '敌人'} 开始播放复活动画`);
            entity.setAnimation('revive');

            // 确保复活动画不循环
            if (entity.currentAnimation) {
                entity.currentAnimation.loop = false;
                entity.currentAnimation.gotoAndPlay(0);
                console.log(`${entity.name || '敌人'} 复活动画设置为不循环`);
            }
        } else {
            console.log(`${entity.name || '敌人'} 没有setAnimation方法`);
        }

        // 复活动画结束后暂停在最后一帧
        const reviveDuration = this.reviveAnimationDuration;
        console.log(`复活动画持续时间: ${reviveDuration}ms`);

        setTimeout(() => {
            if (entity.combat && entity.combat.isReviving) {
                console.log(`${entity.name || '敌人'} 复活动画播放完成，准备暂停在最后一帧`);

                // 停止复活动画并暂停在最后一帧
                if (entity.currentAnimation) {
                    entity.currentAnimation.stop();
                    // 设置为复活动画的最后一帧
                    if (entity.currentAnimation.totalFrames > 0) {
                        entity.currentAnimation.gotoAndStop(entity.currentAnimation.totalFrames - 1);
                        console.log(`${entity.name || '敌人'} 暂停在复活动画第${entity.currentAnimation.totalFrames - 1}帧`);
                    }
                    // 确保动画保持可见
                    entity.currentAnimation.visible = true;
                }

                console.log(`${entity.name || '敌人'} 复活动画暂停在最后一帧，等待2秒`);

                // 暂停2秒后恢复正常状态
                setTimeout(() => {
                    if (entity.combat && entity.combat.isReviving) {
                        console.log(`${entity.name || '敌人'} 2秒暂停结束，开始恢复正常状态`);

                        entity.combat.isReviving = false;
                        entity.combat.currentState = 'idle';

                        // 确保AI状态正确
                        entity.aiState = 'patrol';

                        // 播放idle动画
                        if (entity.setAnimation) {
                            entity.setAnimation('idle');
                        }

                        console.log(`${entity.name || '敌人'} 复活完成！状态: health=${entity.health}, aiState=${entity.aiState}, combatState=${entity.combat.currentState}`);
                    }
                }, 2000); // 暂停2秒
            }
        }, reviveDuration);
    }

    /**
     * 取消敌人的复活定时器
     * @param {Object} entity 敌人实体
     */
    cancelAutoRevive(entity) {
        if (entity.reviveTimer) {
            clearTimeout(entity.reviveTimer);
            entity.reviveTimer = null;
            console.log(`${entity.name || '敌人'} 的复活定时器已取消`);
        }
    }
}
