/**
 * 管理后台工具函数
 * 提供公共的UI和工具函数，供admin.js和map-editor.js等使用
 */

/**
 * 显示模态框
 * @param {string} title 模态框标题
 * @param {string} content 模态框内容HTML
 */
function showModal(title, content) {
    const modalContainer = document.getElementById('modal-container');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');

    if (modalContainer && modalTitle && modalContent) {
        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        modalContainer.classList.add('active');
    } else {
        console.error('模态框元素未找到');
    }
}

/**
 * 关闭模态框
 */
function closeModal() {
    const modalContainer = document.getElementById('modal-container');
    if (modalContainer) {
        modalContainer.classList.remove('active');
    }
}

/**
 * 显示通知
 * @param {string} message 通知消息
 * @param {string} type 通知类型 ('success', 'error', 'warning', 'info')
 * @param {number} duration 显示时长(毫秒)
 */
function showNotification(message, type = 'info', duration = 3000) {
    // 检查是否已有通知容器
    let notificationContainer = document.getElementById('notification-container');
    
    // 如果没有，创建一个
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        document.body.appendChild(notificationContainer);
        
        // 添加样式
        notificationContainer.style.position = 'fixed';
        notificationContainer.style.top = '20px';
        notificationContainer.style.right = '20px';
        notificationContainer.style.zIndex = '9999';
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加样式
    notification.style.padding = '10px 15px';
    notification.style.marginBottom = '10px';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    notification.style.transition = 'all 0.3s ease';
    notification.style.opacity = '0';
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#4CAF50';
            notification.style.color = 'white';
            break;
        case 'error':
            notification.style.backgroundColor = '#F44336';
            notification.style.color = 'white';
            break;
        case 'warning':
            notification.style.backgroundColor = '#FF9800';
            notification.style.color = 'white';
            break;
        case 'info':
        default:
            notification.style.backgroundColor = '#2196F3';
            notification.style.color = 'white';
            break;
    }
    
    // 添加到容器
    notificationContainer.appendChild(notification);
    
    // 显示通知
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 10);
    
    // 设置自动关闭
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            notificationContainer.removeChild(notification);
        }, 300);
    }, duration);
}

/**
 * 确认删除
 * @param {string} type 删除的实体类型
 * @param {number} id 实体ID
 * @param {Function} callback 确认后的回调函数
 */
function confirmDelete(type, id, callback) {
    if (confirm(`确定要删除这个${getEntityTypeName(type)}吗？此操作不可撤销。`)) {
        if (typeof callback === 'function') {
            callback(type, id);
        }
    }
}

/**
 * 获取实体类型名称
 * @param {string} type 实体类型
 * @returns {string} 实体类型的中文名称
 */
function getEntityTypeName(type) {
    switch (type) {
        case 'player': return '玩家';
        case 'enemy': return '敌人';
        case 'sprite': return '精灵表';
        case 'animation': return '动画';
        case 'map': return '地图';
        default: return '项目';
    }
}

/**
 * 删除实体
 * @param {string} type 实体类型
 * @param {number} id 实体ID
 */
async function deleteEntity(type, id) {
    try {
        switch (type) {
            case 'player':
                await entityManager.deletePlayer(id);
                loadPlayers();
                break;
            case 'enemy':
                await entityManager.deleteEnemy(id);
                loadEnemies();
                break;
            case 'sprite':
                await spriteManager.deleteSprite(id);
                loadSprites();
                break;
            case 'animation':
                await spriteManager.deleteAnimation(id);
                loadAnimations();
                break;
            default:
                console.warn(`未知的实体类型: ${type}`);
                return;
        }
        
        // 更新仪表盘
        updateDashboardStats();
        showNotification(`${getEntityTypeName(type)}删除成功`, 'success');
    } catch (error) {
        console.error(`删除${getEntityTypeName(type)}失败:`, error);
        showNotification(`删除失败: ${error.message}`, 'error');
    }
}

/**
 * 将文件转换为Base64
 * @param {File} file 文件对象
 * @returns {Promise<string>} Base64字符串
 */
function convertFileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        reader.onload = () => {
            resolve(reader.result);
        };
        
        reader.onerror = (error) => {
            reject(error);
        };
        
        reader.readAsDataURL(file);
    });
}

// 动画预览相关函数
let previewApp = null;
let previewAnimatedSprite = null;

/**
 * 显示动画预览
 * @param {number} animationId 动画ID
 */
async function showAnimationPreview(animationId) {
    try {
        // 显示预览容器
        const previewContainer = document.getElementById('animation-preview-container');
        previewContainer.classList.add('active');
        
        // 创建PIXI应用
        const canvasContainer = document.getElementById('animation-canvas-container');
        canvasContainer.innerHTML = '';
        
        // 清理旧的应用
        if (previewApp) {
            previewApp.destroy(true);
        }
        
        previewApp = new PIXI.Application({
            width: 200,
            height: 200,
            backgroundColor: 0x333333,
            antialias: true
        });
        
        canvasContainer.appendChild(previewApp.view);
        
        // 创建动画精灵
        previewAnimatedSprite = await spriteManager.createAnimatedSprite(animationId, 'down');
        previewAnimatedSprite.x = previewApp.screen.width / 2;
        previewAnimatedSprite.y = previewApp.screen.height / 2;
        previewAnimatedSprite.anchor.set(0.5);
        
        previewApp.stage.addChild(previewAnimatedSprite);
        
        // 自动播放
        previewAnimatedSprite.play();
    } catch (error) {
        console.error('显示动画预览失败:', error);
        showNotification('显示动画预览失败: ' + error.message, 'error');
    }
}

/**
 * 播放动画预览
 */
function playAnimationPreview() {
    if (previewAnimatedSprite) {
        previewAnimatedSprite.play();
    }
}

/**
 * 停止动画预览
 */
function stopAnimationPreview() {
    if (previewAnimatedSprite) {
        previewAnimatedSprite.stop();
    }
}

/**
 * 关闭动画预览
 */
function closeAnimationPreview() {
    const previewContainer = document.getElementById('animation-preview-container');
    previewContainer.classList.remove('active');
    
    // 清理资源
    if (previewApp) {
        previewApp.destroy(true);
        previewApp = null;
        previewAnimatedSprite = null;
    }
}
