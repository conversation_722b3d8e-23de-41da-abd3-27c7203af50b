# 2D冒险游戏 - 战斗系统

## 概述

本项目实现了一个完整的2D冒险游戏战斗系统，将管理后台中设置的所有参数（碰撞框、攻击范围、警戒范围等）应用到实际游戏中，并添加了基于圆形的碰撞检测系统。

## 新增功能

### 1. 碰撞系统 (CollisionSystem)
- **圆形碰撞检测**：使用圆形进行更精确的碰撞检测
- **碰撞框偏移**：支持碰撞框相对于实体中心的位置偏移
- **多种范围检测**：支持碰撞框、攻击范围、警戒范围检测
- **墙壁碰撞**：与地图墙壁的碰撞检测
- **调试模式**：可视化显示所有碰撞框和范围

### 2. 战斗系统 (CombatSystem)
- **攻击冷却**：防止连续攻击的冷却机制
- **伤害计算**：基于攻击力和防御力的伤害计算
- **生命值管理**：实体生命值和死亡处理
- **伤害数字**：显示伤害数值的视觉效果
- **攻击动画**：攻击和受伤的视觉反馈

### 3. AI系统
- **多状态AI**：巡逻、警戒、追击、攻击等状态
- **警戒范围**：敌人可以在警戒范围内发现玩家
- **智能追击**：敌人会追击进入警戒范围的玩家
- **路径跟随**：敌人可以沿着预设路径移动

## 技术实现

### 碰撞检测算法
```javascript
// 圆形碰撞检测
checkCollision(entity1, entity2) {
    const center1 = this.getCollisionCenter(entity1);
    const center2 = this.getCollisionCenter(entity2);
    const radius1 = this.getCollisionRadius(entity1);
    const radius2 = this.getCollisionRadius(entity2);
    
    const distance = this.getDistance(center1, center2);
    return distance < (radius1 + radius2);
}
```

### 战斗系统流程
1. **攻击检测**：检查攻击者是否在攻击范围内
2. **冷却检查**：验证攻击冷却时间
3. **伤害计算**：计算实际伤害值
4. **应用伤害**：减少目标生命值
5. **视觉反馈**：显示伤害数字和动画效果

### 实体数据结构
```javascript
// 玩家/敌人实体
{
    // 基础属性
    name: "实体名称",
    health: 100,
    maxHealth: 100,
    attack: 15,
    defense: 5,
    speed: 3,
    
    // 碰撞框（支持偏移）
    collisionBox: {
        width: 40,
        height: 40,
        offsetX: 0,
        offsetY: 0
    },
    
    // 攻击范围
    attackRange: {
        width: 80,
        height: 80
    },
    
    // 警戒范围（仅敌人）
    alertRange: {
        width: 120,
        height: 120
    }
}
```

## 游戏机制

### 玩家控制
- **移动**：WASD 或方向键
- **攻击**：空格键
- **碰撞**：与敌人和墙壁的碰撞检测
- **生命值**：受到攻击时减少，死亡时游戏结束

### 敌人AI行为
1. **巡逻状态**：沿路径移动或随机移动
2. **警戒状态**：发现玩家进入警戒范围
3. **追击状态**：主动追击玩家
4. **攻击状态**：在攻击范围内攻击玩家

### 战斗机制
- **攻击判定**：基于圆形范围检测
- **伤害计算**：攻击力 - 防御力 + 随机因素
- **攻击冷却**：防止无限连击
- **击退效果**：受击时的轻微位移
- **死亡处理**：生命值归零时的死亡动画

## 后台集成

### 实体参数应用
- **碰撞框设置**：宽度、高度、X偏移、Y偏移
- **攻击范围设置**：宽度、高度
- **警戒范围设置**：宽度、高度（仅敌人）
- **基础属性**：生命值、攻击力、防御力、速度

### 动画系统
- **待机动画**：实体静止时的动画
- **移动动画**：实体移动时的动画
- **攻击动画**：实体攻击时的动画
- **死亡动画**：实体死亡时的动画

## 测试页面

### test-combat.html
独立的战斗系统测试页面，包含：
- **调试模式**：显示/隐藏碰撞框和范围
- **游戏控制**：重新开始、添加敌人、治疗玩家
- **实时战斗**：完整的战斗系统演示

### 测试功能
- **碰撞检测**：测试圆形碰撞检测
- **攻击系统**：测试攻击范围和伤害计算
- **AI行为**：测试敌人的警戒和追击
- **视觉反馈**：测试伤害数字和动画效果

## 使用方法

### 1. 在管理后台设置实体参数
1. 打开 `admin.html`
2. 在玩家/敌人管理中设置碰撞框、攻击范围等参数
3. 使用预览功能查看范围效果

### 2. 在游戏中体验战斗
1. 打开 `index.html` 开始游戏
2. 使用WASD移动，空格攻击
3. 观察敌人的AI行为和战斗效果

### 3. 测试战斗系统
1. 打开 `test-combat.html`
2. 启用调试模式查看碰撞框
3. 测试各种战斗功能

## 技术特点

- **高性能**：使用PIXI.js进行硬件加速渲染
- **模块化**：碰撞系统和战斗系统独立可复用
- **可扩展**：易于添加新的AI状态和战斗机制
- **调试友好**：完整的调试模式和可视化工具
- **数据驱动**：所有参数来自管理后台设置

## 文件结构

```
js/
├── collision-system.js    # 碰撞检测系统
├── combat-system.js       # 战斗系统
├── game.js               # 主游戏逻辑（已更新）
├── player.js             # 玩家类（已更新）
└── enemy.js              # 敌人类（已更新）

test-combat.html          # 战斗系统测试页面
COMBAT_SYSTEM_README.md   # 本文档
```

## 未来扩展

- **技能系统**：添加特殊技能和魔法
- **装备系统**：武器和防具影响战斗属性
- **经验系统**：击败敌人获得经验和升级
- **多种敌人类型**：不同行为模式的敌人
- **环境交互**：可破坏的环境和陷阱
