/**
 * 玩家类
 */
class Player {
    /**
     * 创建玩家
     * @param {PIXI.Application} app PIXI应用
     * @param {GameMap} gameMap 游戏地图
     * @param {Object} entity 玩家实体数据（可选）
     */
    constructor(app, gameMap, entity = null) {
        this.app = app;
        this.gameMap = gameMap;
        this.sprite = null;
        this.animatedSprites = {};
        this.currentAnimation = null;
        this.direction = 'down'; // 默认朝下

        // 战斗系统引用（将在游戏初始化时设置）
        this.combatSystem = null;
        this.collisionSystem = null;

        if (entity) {
            // 使用实体数据
            this.name = entity.name;
            this.health = entity.health;
            this.maxHealth = entity.maxHealth;
            this.attack = entity.attack;
            this.defense = entity.defense;
            this.speed = entity.speed;
            // 移除整体的宽度和高度
            // this.width = entity.width;
            // this.height = entity.height;
            this.animations = entity.animations;
            // 动画尺寸配置，包含每个动画的宽度和高度
            this.animationSizes = entity.animationSizes || {};
            // 默认动画尺寸
            this.defaultAnimationSize = {
                width: entity.defaultAnimationWidth || 60,
                height: entity.defaultAnimationHeight || 60
            };

            // 计算初始坐标时，使用默认动画尺寸
            this.x = entity.x || this.app.screen.width / 2 - this.defaultAnimationSize.width / 2;
            this.y = entity.y || this.app.screen.height / 2 - this.defaultAnimationSize.height / 2;
        } else {
            // 使用默认值
            this.name = "玩家";
            this.health = 100;
            this.maxHealth = 100;
            this.attack = 10;
            this.defense = 5;
            this.speed = 4;
            // 移除整体的宽度和高度
            // this.width = 60;
            // this.height = 60;
            this.animations = {};
            // 动画尺寸配置，包含每个动画的宽度和高度
            this.animationSizes = {};
            // 默认动画尺寸
            this.defaultAnimationSize = {
                width: 60,
                height: 60
            };

            // 设置玩家初始位置在地图中心附近，使用默认动画尺寸
            this.x = this.app.screen.width / 2 - this.defaultAnimationSize.width / 2;
            this.y = this.app.screen.height / 2 - this.defaultAnimationSize.height / 2;
        }

        // 实体类型标识
        this.type = 'player';

        this.keys = {};
        this.keyPressed = {}; // 跟踪按键是否刚被按下

        this.init();
    }

    /**
     * 设置战斗系统
     * @param {CombatSystem} combatSystem 战斗系统
     * @param {CollisionSystem} collisionSystem 碰撞系统
     */
    setCombatSystems(combatSystem, collisionSystem) {
        this.combatSystem = combatSystem;
        this.collisionSystem = collisionSystem;

        // 初始化战斗属性
        if (this.combatSystem) {
            this.combatSystem.initEntityCombat(this);
        }
    }

    /**
     * 初始化玩家
     */
    init() {
        // 检查是否有动画
        if (this.animations && Object.keys(this.animations).length > 0) {
            // 使用默认精灵作为占位符，直到动画加载完成
            this.createDefaultSprite();

            // 加载动画
            this.loadAnimations();
        } else {
            // 没有动画，使用默认精灵
            this.createDefaultSprite();
        }

        // 设置键盘监听
        this.setupKeyboardListeners();
    }

    /**
     * 创建默认精灵
     */
    createDefaultSprite() {
        // 创建一个简单的图形作为玩家精灵
        this.sprite = new PIXI.Graphics();
        this.sprite.beginFill(0x3498db);
        // 使用默认动画尺寸
        this.sprite.drawRect(0, 0, this.defaultAnimationSize.width, this.defaultAnimationSize.height);
        this.sprite.endFill();

        // 设置位置
        this.sprite.x = this.x;
        this.sprite.y = this.y;

        // 添加到舞台
        this.app.stage.addChild(this.sprite);
    }

    /**
     * 加载动画
     */
    async loadAnimations() {
        // 等待游戏实例初始化完成
        let attempts = 0;
        const maxAttempts = 10;

        while (!window.game || !window.game.spriteManager) {
            if (attempts >= maxAttempts) {
                console.warn('无法加载玩家动画：找不到精灵管理器');
                return;
            }

            console.log('等待精灵管理器初始化...');
            await new Promise(resolve => setTimeout(resolve, 500));
            attempts++;
        }

        console.log('精灵管理器已找到，开始加载玩家动画');
        const spriteManager = window.game.spriteManager;

        // 加载所有动画
        for (const [type, animationId] of Object.entries(this.animations)) {
            if (!animationId) continue;

            try {
                // 为每个方向创建动画精灵
                const directions = ['down', 'up', 'left', 'right'];

                for (const direction of directions) {
                    try {
                        const animatedSprite = await spriteManager.createAnimatedSprite(animationId, direction);

                        if (!this.animatedSprites[type]) {
                            this.animatedSprites[type] = {};
                        }

                        this.animatedSprites[type][direction] = animatedSprite;

                        // 设置锚点为中心，确保所有动画都以相同方式对齐
                        animatedSprite.anchor.set(0.5, 0.5);

                        // 设置位置（所有动画都使用默认动画尺寸的中心作为基准点）
                        animatedSprite.x = this.x + this.defaultAnimationSize.width / 2;
                        animatedSprite.y = this.y + this.defaultAnimationSize.height / 2;

                        // 获取该动画类型的尺寸配置，如果没有则使用默认
                        const size = this.animationSizes[type] || this.defaultAnimationSize;
                        animatedSprite.width = size.width;
                        animatedSprite.height = size.height;

                        // 默认隐藏
                        animatedSprite.visible = false;

                        // 添加到舞台
                        this.app.stage.addChild(animatedSprite);
                    } catch (error) {
                        console.warn(`无法创建${direction}方向的${type}动画:`, error);
                    }
                }

                console.log(`玩家${type}动画加载成功`);
            } catch (error) {
                console.error(`加载玩家${type}动画失败:`, error);
            }
        }

        // 设置默认动画
        this.setAnimation('idle');
    }

    /**
     * 设置当前动画
     * @param {string} type 动画类型
     */
    setAnimation(type) {
        // 如果没有动画，直接返回
        if (!this.animatedSprites[type] || !this.animatedSprites[type][this.direction]) {
            return;
        }

        // 停止当前动画
        if (this.currentAnimation) {
            this.currentAnimation.visible = false;
            this.currentAnimation.stop();
        }

        // 隐藏默认精灵
        if (this.sprite) {
            this.sprite.visible = false;
        }

        // 设置新动画
        this.currentAnimation = this.animatedSprites[type][this.direction];
        this.currentAnimation.visible = true;

        // 确保动画位置正确（所有动画都使用默认动画尺寸的中心作为基准点）
        this.currentAnimation.x = this.x + this.defaultAnimationSize.width / 2;
        this.currentAnimation.y = this.y + this.defaultAnimationSize.height / 2;

        // 设置动画循环模式
        if (type === 'attack' || type === 'skill' || type === 'death' || type === 'revive') {
            // 攻击、技能、死亡、复活动画只播放一次
            this.currentAnimation.loop = false;
            this.currentAnimation.gotoAndPlay(0);
            console.log(`播放${type}动画，不循环`);
        } else {
            // 其他动画（idle、move）循环播放
            this.currentAnimation.loop = true;
            this.currentAnimation.play();
        }
    }

    /**
     * 设置方向
     * @param {string} direction 方向
     */
    setDirection(direction) {
        // 如果方向相同，不做任何改变
        if (this.direction === direction) {
            return;
        }

        this.direction = direction;

        // 更新当前动画
        if (this.currentAnimation) {
            const currentType = Object.keys(this.animatedSprites).find(type =>
                Object.values(this.animatedSprites[type]).includes(this.currentAnimation)
            );

            if (currentType && this.animatedSprites[currentType][direction]) {
                // 停止当前动画
                this.currentAnimation.visible = false;
                this.currentAnimation.stop();

                // 设置新方向的动画
                this.currentAnimation = this.animatedSprites[currentType][direction];
                this.currentAnimation.visible = true;

                // 确保动画位置正确（所有动画都使用默认动画尺寸的中心作为基准点）
                this.currentAnimation.x = this.x + this.defaultAnimationSize.width / 2;
                this.currentAnimation.y = this.y + this.defaultAnimationSize.height / 2;

                this.currentAnimation.play();
            }
        }
    }

    setupKeyboardListeners() {
        // 键盘按下事件
        window.addEventListener('keydown', (e) => {
            if (!this.keys[e.key]) {
                this.keyPressed[e.key] = true; // 标记为刚被按下
            }
            this.keys[e.key] = true;
        });

        // 键盘释放事件
        window.addEventListener('keyup', (e) => {
            this.keys[e.key] = false;
            this.keyPressed[e.key] = false;
        });
    }

    /**
     * 更新玩家状态
     */
    update() {
        // 保存当前位置用于碰撞检测
        const prevX = this.x;
        const prevY = this.y;

        // 记录是否移动
        let isMoving = false;
        let newDirection = this.direction;

        // 检查战斗状态，如果正在攻击、使用技能、死亡或复活，则不能移动
        const canMove = !this.combatSystem ||
                       (!this.combatSystem.isDead(this) &&
                        this.combatSystem.getCombatState(this) === 'idle');

        // 根据按键移动（只有在可以移动时才移动）
        if (canMove) {
            if (this.keys['ArrowUp'] || this.keys['w']) {
                this.y -= this.speed;
                newDirection = 'up';
                isMoving = true;
            }
            if (this.keys['ArrowDown'] || this.keys['s']) {
                this.y += this.speed;
                newDirection = 'down';
                isMoving = true;
            }
            if (this.keys['ArrowLeft'] || this.keys['a']) {
                this.x -= this.speed;
                newDirection = 'left';
                isMoving = true;
            }
            if (this.keys['ArrowRight'] || this.keys['d']) {
                this.x += this.speed;
                newDirection = 'right';
                isMoving = true;
            }
        }

        // 处理攻击和技能攻击
        this.handleCombatInput();

        // 检查碰撞
        // 获取当前动画的尺寸，或使用默认尺寸
        let currentSize = this.defaultAnimationSize;

        if (this.currentAnimation) {
            const currentType = Object.keys(this.animatedSprites).find(type =>
                Object.values(this.animatedSprites[type]).includes(this.currentAnimation)
            );

            if (currentType) {
                currentSize = this.animationSizes[currentType] || this.defaultAnimationSize;
            }
        }

        const playerBounds = {
            x: this.x,
            y: this.y,
            width: currentSize.width,
            height: currentSize.height
        };

        if (this.gameMap.checkCollision(playerBounds)) {
            // 如果发生碰撞，恢复之前的位置
            this.x = prevX;
            this.y = prevY;
            isMoving = false;
        }

        // 更新方向
        if (newDirection !== this.direction) {
            this.setDirection(newDirection);
        }

        // 更新动画（只有在空闲状态时才更新移动/待机动画，不干扰战斗动画）
        const combatState = this.combatSystem ? this.combatSystem.getCombatState(this) : 'idle';
        const shouldUpdateAnimation = canMove && (combatState === 'idle');

        if (shouldUpdateAnimation) {
            if (isMoving) {
                // 如果有移动动画，使用移动动画
                if (this.animatedSprites.move && this.animatedSprites.move[this.direction]) {
                    this.setAnimation('move');
                }
            } else {
                // 如果有待机动画，使用待机动画
                if (this.animatedSprites.idle && this.animatedSprites.idle[this.direction]) {
                    this.setAnimation('idle');
                }
            }
        }
        // 注意：攻击和技能动画由战斗系统直接控制，不在这里处理

        // 更新精灵位置（考虑锚点为中心）
        if (this.sprite) {
            this.sprite.x = this.x + this.defaultAnimationSize.width / 2;
            this.sprite.y = this.y + this.defaultAnimationSize.height / 2;
        }

        // 更新动画精灵位置（所有动画都使用默认动画尺寸的中心作为基准点）
        if (this.currentAnimation) {
            this.currentAnimation.x = this.x + this.defaultAnimationSize.width / 2;
            this.currentAnimation.y = this.y + this.defaultAnimationSize.height / 2;
        }

        // 确保所有动画精灵的位置都是正确的（所有动画都使用默认动画尺寸的中心作为基准点）
        for (const type in this.animatedSprites) {
            for (const direction in this.animatedSprites[type]) {
                const sprite = this.animatedSprites[type][direction];
                if (sprite) {
                    sprite.x = this.x + this.defaultAnimationSize.width / 2;
                    sprite.y = this.y + this.defaultAnimationSize.height / 2;
                }
            }
        }
    }

    /**
     * 处理战斗输入
     */
    handleCombatInput() {
        if (!this.combatSystem) {
            return;
        }

        // 空格键攻击（只在刚按下时触发）
        if (this.keyPressed[' '] || this.keyPressed['Space']) {
            this.performAttackOnNearestEnemy();
            this.keyPressed[' '] = false;
            this.keyPressed['Space'] = false;
        }

        // Q键技能攻击（只在刚按下时触发）
        if (this.keyPressed['q'] || this.keyPressed['Q']) {
            this.performSkillAttackOnNearestEnemy();
            this.keyPressed['q'] = false;
            this.keyPressed['Q'] = false;
        }

        // R键复活（只在刚按下时触发）
        if (this.keyPressed['r'] || this.keyPressed['R']) {
            if (this.combatSystem.isDead(this)) {
                this.combatSystem.reviveEntity(this);
                // 重置游戏状态
                if (window.game && window.game.gameState) {
                    window.game.gameState.gameOver = false;
                }
            }
            this.keyPressed['r'] = false;
            this.keyPressed['R'] = false;
        }
    }

    /**
     * 执行攻击动作（总是播放攻击动画，但只对范围内的敌人造成伤害）
     */
    performAttackOnNearestEnemy() {
        // 检查是否可以攻击
        if (!this.combatSystem || !this.combatSystem.canAttack(this)) {
            return;
        }

        // 设置攻击状态并播放攻击动画
        this.combatSystem.initEntityCombat(this);
        this.combatSystem.startAttackAnimation(this);

        // 查找攻击范围内的敌人并造成伤害
        const enemiesInRange = this.findEnemiesInAttackRange();
        for (const enemy of enemiesInRange) {
            const damage = this.combatSystem.calculateDamage(this.attack || 10, enemy.defense || 0);
            this.combatSystem.applyDamage(enemy, damage);
            this.combatSystem.showDamageNumber(enemy, damage);
        }
    }

    /**
     * 执行技能攻击动作（总是播放技能动画，但只对范围内的敌人造成伤害）
     */
    performSkillAttackOnNearestEnemy() {
        // 检查是否可以使用技能
        if (!this.combatSystem || !this.combatSystem.canUseSkill(this)) {
            return;
        }

        // 设置技能攻击状态并播放技能动画
        this.combatSystem.initEntityCombat(this);
        this.combatSystem.startSkillAnimation(this);

        // 查找技能攻击范围内的敌人并造成两段伤害
        const enemiesInRange = this.findEnemiesInSkillRange();
        for (const enemy of enemiesInRange) {
            // 技能攻击造成两段伤害，总伤害为攻击力的300%
            this.combatSystem.performTwoStageSkillDamage(this, enemy);
        }
    }

    /**
     * 查找攻击范围内的所有敌人
     * @returns {Array} 攻击范围内的敌人数组
     */
    findEnemiesInAttackRange() {
        if (!this.collisionSystem || !window.game || !window.game.enemies) {
            return [];
        }

        const enemiesInRange = [];

        for (const enemy of window.game.enemies) {
            // 跳过已死亡的敌人
            if (this.combatSystem.isDead(enemy)) {
                continue;
            }

            // 检查是否在攻击范围内
            if (this.collisionSystem.checkAttackHit(this, enemy)) {
                enemiesInRange.push(enemy);
            }
        }

        return enemiesInRange;
    }

    /**
     * 查找技能攻击范围内的所有敌人
     * @returns {Array} 技能攻击范围内的敌人数组
     */
    findEnemiesInSkillRange() {
        if (!this.collisionSystem || !window.game || !window.game.enemies) {
            return [];
        }

        const enemiesInRange = [];

        for (const enemy of window.game.enemies) {
            // 跳过已死亡的敌人
            if (this.combatSystem.isDead(enemy)) {
                continue;
            }

            // 检查是否在技能攻击范围内
            if (this.collisionSystem.checkSkillHit(this, enemy)) {
                enemiesInRange.push(enemy);
            }
        }

        return enemiesInRange;
    }

    /**
     * 查找攻击范围内最近的敌人（保留用于其他用途）
     * @returns {Object|null} 最近的敌人或null
     */
    findNearestEnemyInRange() {
        const enemiesInRange = this.findEnemiesInAttackRange();

        if (enemiesInRange.length === 0) {
            return null;
        }

        let nearestEnemy = null;
        let nearestDistance = Infinity;

        for (const enemy of enemiesInRange) {
            const distance = this.collisionSystem.getDistance(this, enemy);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestEnemy = enemy;
            }
        }

        return nearestEnemy;
    }

    // 设置玩家纹理
    setTexture(texture) {
        // 移除旧的精灵
        if (this.sprite) {
            this.app.stage.removeChild(this.sprite);
        }

        // 创建新的精灵
        this.sprite = new PIXI.Sprite(texture);
        // 设置锚点为中心，与动画精灵保持一致
        this.sprite.anchor.set(0.5, 0.5);
        // 使用默认动画尺寸
        this.sprite.width = this.defaultAnimationSize.width;
        this.sprite.height = this.defaultAnimationSize.height;
        this.sprite.x = this.x + this.defaultAnimationSize.width / 2;
        this.sprite.y = this.y + this.defaultAnimationSize.height / 2;

        // 添加到舞台
        this.app.stage.addChild(this.sprite);
    }

    // 保留旧方法以兼容性
    loadTexture(texturePath) {
        try {
            const texture = PIXI.Texture.from(texturePath);
            this.setTexture(texture);
        } catch (error) {
            console.error("加载玩家纹理失败:", error);
        }
    }
}
