/**
 * 精灵动画管理类
 * 用于管理游戏中的精灵表和动画
 */
class SpriteManager {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.sprites = {};
        this.animations = {};
        this.loadedTextures = {};

        // 动画类型
        this.animationTypes = {
            IDLE: 'idle',         // 待机
            MOVE: 'move',         // 移动
            ATTACK: 'attack',     // 攻击
            SKILL: 'skill',       // 技能攻击
            DEATH: 'death',       // 死亡
            REVIVE: 'revive'      // 复活
        };

        // 方向
        this.directions = {
            UP: 'up',
            RIGHT: 'right',
            DOWN: 'down',
            LEFT: 'left'
        };
    }

    /**
     * 初始化精灵管理器
     */
    async initialize() {
        try {
            // 加载所有精灵表数据
            const sprites = await this.dbManager.getAllData(this.dbManager.stores.sprites);
            sprites.forEach(sprite => {
                this.sprites[sprite.id] = sprite;
            });

            // 加载所有动画数据
            const animations = await this.dbManager.getAllData(this.dbManager.stores.animations);
            animations.forEach(animation => {
                this.animations[animation.id] = animation;
            });

            console.log('精灵管理器初始化完成');
            return true;
        } catch (error) {
            console.error('精灵管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 创建新的精灵表
     * @param {Object} spriteData 精灵表数据
     * @returns {Promise} 创建操作的Promise
     */
    async createSprite(spriteData) {
        try {
            const id = await this.dbManager.addData(this.dbManager.stores.sprites, spriteData);
            spriteData.id = id;
            this.sprites[id] = spriteData;
            return id;
        } catch (error) {
            console.error('创建精灵表失败:', error);
            throw error;
        }
    }

    /**
     * 更新精灵表
     * @param {Object} spriteData 精灵表数据
     * @returns {Promise} 更新操作的Promise
     */
    async updateSprite(spriteData) {
        try {
            await this.dbManager.updateData(this.dbManager.stores.sprites, spriteData);
            this.sprites[spriteData.id] = spriteData;
            return spriteData;
        } catch (error) {
            console.error('更新精灵表失败:', error);
            throw error;
        }
    }

    /**
     * 删除精灵表
     * @param {number} id 精灵表ID
     * @returns {Promise} 删除操作的Promise
     */
    async deleteSprite(id) {
        try {
            await this.dbManager.deleteData(this.dbManager.stores.sprites, id);
            delete this.sprites[id];

            // 同时删除关联的动画
            const animations = await this.dbManager.getAllData(this.dbManager.stores.animations);
            const relatedAnimations = animations.filter(anim => anim.spriteId === id);

            for (const anim of relatedAnimations) {
                await this.dbManager.deleteData(this.dbManager.stores.animations, anim.id);
                delete this.animations[anim.id];
            }

            return true;
        } catch (error) {
            console.error('删除精灵表失败:', error);
            throw error;
        }
    }

    /**
     * 创建新的动画
     * @param {Object} animationData 动画数据
     * @returns {Promise} 创建操作的Promise
     */
    async createAnimation(animationData) {
        try {
            const id = await this.dbManager.addData(this.dbManager.stores.animations, animationData);
            animationData.id = id;
            this.animations[id] = animationData;
            return id;
        } catch (error) {
            console.error('创建动画失败:', error);
            throw error;
        }
    }

    /**
     * 更新动画
     * @param {Object} animationData 动画数据
     * @returns {Promise} 更新操作的Promise
     */
    async updateAnimation(animationData) {
        try {
            await this.dbManager.updateData(this.dbManager.stores.animations, animationData);
            this.animations[animationData.id] = animationData;
            return animationData;
        } catch (error) {
            console.error('更新动画失败:', error);
            throw error;
        }
    }

    /**
     * 删除动画
     * @param {number} id 动画ID
     * @returns {Promise} 删除操作的Promise
     */
    async deleteAnimation(id) {
        try {
            await this.dbManager.deleteData(this.dbManager.stores.animations, id);
            delete this.animations[id];
            return true;
        } catch (error) {
            console.error('删除动画失败:', error);
            throw error;
        }
    }

    /**
     * 加载精灵表纹理
     * @param {number} spriteId 精灵表ID
     * @returns {Promise} 加载操作的Promise
     */
    async loadSpriteTexture(spriteId) {
        try {
            console.log(`加载精灵表纹理，ID: ${spriteId}`);

            const sprite = this.sprites[spriteId];
            if (!sprite) {
                throw new Error(`未找到ID为${spriteId}的精灵表`);
            }

            if (this.loadedTextures[spriteId]) {
                console.log('使用缓存的纹理');
                return this.loadedTextures[spriteId];
            }

            // 检查图片路径是否有效
            if (!sprite.imagePath) {
                throw new Error(`精灵表 ${spriteId} 没有有效的图片路径`);
            }

            console.log(`加载图片，路径类型: ${sprite.imagePath.substring(0, 30)}...`);

            // 检查是否是Base64图像
            const isBase64 = sprite.imagePath.startsWith('data:image/');
            if (isBase64) {
                console.log('检测到Base64图像');
            }

            // 使用Promise包装纹理加载过程
            return new Promise((resolve, reject) => {
                try {
                    // 创建一个图像元素来预加载
                    const img = new Image();

                    img.onload = () => {
                        try {
                            console.log('图像加载成功，创建纹理');
                            // 使用加载好的图像创建纹理
                            const texture = PIXI.Texture.from(img);
                            this.loadedTextures[spriteId] = texture;
                            resolve(texture);
                        } catch (error) {
                            console.error('从图像创建纹理失败:', error);
                            reject(error);
                        }
                    };

                    img.onerror = (error) => {
                        console.error('图像加载失败:', error);
                        reject(new Error(`加载精灵表 ${spriteId} 的图像失败`));
                    };

                    // 设置图像源
                    img.src = sprite.imagePath;

                    // 如果图像已经加载完成，手动触发onload
                    if (img.complete) {
                        img.onload();
                    }
                } catch (error) {
                    console.error('创建纹理失败:', error);
                    reject(error);
                }
            });
        } catch (error) {
            console.error('加载精灵表纹理失败:', error);
            throw error;
        }
    }

    /**
     * 创建精灵动画
     * @param {number} animationId 动画ID
     * @param {string} direction 方向 (up, right, down, left)
     * @returns {Object} 动画对象
     */
    async createAnimatedSprite(animationId, direction = 'down') {
        try {
            console.log(`创建动画，ID: ${animationId}，方向: ${direction}`);

            const animation = this.animations[animationId];
            if (!animation) {
                throw new Error(`未找到ID为${animationId}的动画`);
            }
            console.log('找到动画数据:', animation);

            const sprite = this.sprites[animation.spriteId];
            if (!sprite) {
                throw new Error(`未找到ID为${animation.spriteId}的精灵表`);
            }
            console.log('找到精灵表数据:', sprite);

            // 加载纹理
            const baseTexture = await this.loadSpriteTexture(animation.spriteId);
            console.log('精灵表纹理加载成功');

            // 获取方向的帧序列
            const dirFrames = animation.frames[direction] || animation.frames.down;
            if (!dirFrames || dirFrames.length === 0) {
                throw new Error(`动画${animationId}没有${direction}方向的帧`);
            }
            console.log(`${direction}方向的帧序列:`, dirFrames);

            // 创建纹理数组
            const textures = [];
            for (const frameIndex of dirFrames) {
                // 计算帧在精灵表中的位置
                const col = frameIndex % sprite.columns;
                const row = Math.floor(frameIndex / sprite.columns);

                console.log(`帧 ${frameIndex} 位置: 行=${row}, 列=${col}`);

                const frameTexture = new PIXI.Texture(
                    baseTexture.baseTexture,
                    new PIXI.Rectangle(
                        col * sprite.frameWidth,
                        row * sprite.frameHeight,
                        sprite.frameWidth,
                        sprite.frameHeight
                    )
                );

                textures.push(frameTexture);
            }

            console.log(`创建了 ${textures.length} 个帧纹理`);

            // 创建动画精灵
            const animatedSprite = new PIXI.AnimatedSprite(textures);
            animatedSprite.animationSpeed = animation.speed;
            animatedSprite.loop = animation.loop;

            console.log('动画精灵创建成功');
            return animatedSprite;
        } catch (error) {
            console.error('创建精灵动画失败:', error);
            throw error;
        }
    }
}
