/**
 * 地图编辑器类
 * 用于在管理后台编辑游戏地图
 */
class MapEditor {
    constructor(mapManager, entityManager) {
        this.mapManager = mapManager;
        this.entityManager = entityManager;

        // 编辑器容器
        this.container = document.getElementById('map-editor-canvas');

        // 当前地图数据
        this.currentMap = null;
        this.currentMapId = null;

        // 网格设置
        this.gridSize = 80;
        this.mapWidth = 16;
        this.mapHeight = 12;

        // 编辑器状态
        this.currentTool = null;
        this.isDrawing = false;
        this.selectedEnemyId = null;
        this.currentPathId = null;

        // 初始化编辑器
        this.init();
    }

    /**
     * 初始化编辑器
     */
    init() {
        // 设置编辑器工具事件
        this.setupToolEvents();

        // 创建空白地图
        this.createEmptyMap();

        // 添加全局鼠标事件
        document.addEventListener('mouseup', () => {
            this.isDrawing = false;
        });

        document.addEventListener('mouseleave', () => {
            this.isDrawing = false;
        });
    }

    /**
     * 设置编辑器工具事件
     */
    setupToolEvents() {
        // 地图工具
        document.getElementById('tool-add-wall').addEventListener('click', () => this.setTool('add-wall'));
        document.getElementById('tool-remove-wall').addEventListener('click', () => this.setTool('remove-wall'));
        document.getElementById('tool-clear-walls').addEventListener('click', () => this.clearWalls());
        document.getElementById('tool-set-background').addEventListener('click', () => this.showBackgroundSelector());

        // 实体工具
        document.getElementById('tool-add-enemy').addEventListener('click', () => this.showEnemySelector());
        document.getElementById('tool-remove-enemy').addEventListener('click', () => this.setTool('remove-enemy'));
        document.getElementById('tool-clear-enemies').addEventListener('click', () => this.clearEnemies());
        document.getElementById('tool-set-player-start').addEventListener('click', () => this.setTool('set-player-start'));

        // 路径工具
        document.getElementById('tool-draw-path').addEventListener('click', () => this.setTool('draw-path'));
        document.getElementById('tool-clear-paths').addEventListener('click', () => this.clearPaths());

        // 网格设置
        document.getElementById('apply-grid-settings').addEventListener('click', () => this.applyGridSettings());

        // 地图操作
        document.getElementById('save-map').addEventListener('click', () => this.saveMap());
        document.getElementById('test-map').addEventListener('click', () => this.testMap());

        // 创建地图按钮
        document.getElementById('create-map-btn').addEventListener('click', () => this.showMapForm());

        // 添加地图按钮
        document.getElementById('add-map-btn').addEventListener('click', () => this.showMapForm());
    }

    /**
     * 设置当前工具
     * @param {string} tool 工具名称
     */
    setTool(tool) {
        // 移除所有工具按钮的活动状态
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));

        // 设置当前工具
        this.currentTool = tool;

        // 添加当前工具按钮的活动状态
        const toolBtn = document.getElementById(`tool-${tool}`);
        if (toolBtn) {
            toolBtn.classList.add('active');
        }

        console.log(`当前工具: ${tool}`);
    }

    /**
     * 创建空白地图
     */
    createEmptyMap() {
        // 清空容器
        this.container.innerHTML = '';

        // 设置容器大小
        this.container.style.width = `${this.mapWidth * this.gridSize}px`;
        this.container.style.height = `${this.mapHeight * this.gridSize}px`;

        // 创建网格
        for (let y = 0; y < this.mapHeight; y++) {
            for (let x = 0; x < this.mapWidth; x++) {
                const cell = document.createElement('div');
                cell.className = 'map-grid-cell';
                cell.dataset.x = x;
                cell.dataset.y = y;
                cell.style.width = `${this.gridSize}px`;
                cell.style.height = `${this.gridSize}px`;
                cell.style.left = `${x * this.gridSize}px`;
                cell.style.top = `${y * this.gridSize}px`;

                // 添加单元格事件
                this.addCellEvents(cell);

                this.container.appendChild(cell);
            }
        }

        // 创建默认地图数据
        this.currentMap = {
            name: '新地图',
            gridSize: this.gridSize,
            width: this.mapWidth,
            height: this.mapHeight,
            walls: this.mapManager.createEmptyWallsData(this.mapWidth, this.mapHeight),
            enemies: [],
            playerStart: { x: 1, y: 1 },
            paths: []
        };

        // 更新地图显示
        this.updateMapDisplay();
    }

    /**
     * 添加单元格事件
     * @param {HTMLElement} cell 网格单元格元素
     */
    addCellEvents(cell) {
        // 鼠标按下事件
        cell.addEventListener('mousedown', (e) => {
            e.preventDefault();
            this.isDrawing = true;
            this.handleCellAction(cell);
        });

        // 鼠标移动事件
        cell.addEventListener('mouseover', () => {
            if (this.isDrawing) {
                this.handleCellAction(cell);
            }
        });

        // 鼠标双击事件
        cell.addEventListener('dblclick', () => {
            // 双击删除敌人
            if (this.currentTool === 'remove-enemy') {
                const x = parseInt(cell.dataset.x);
                const y = parseInt(cell.dataset.y);
                this.removeEnemyAt(x, y);
            }
        });
    }

    /**
     * 处理单元格操作
     * @param {HTMLElement} cell 网格单元格元素
     */
    handleCellAction(cell) {
        const x = parseInt(cell.dataset.x);
        const y = parseInt(cell.dataset.y);

        switch (this.currentTool) {
            case 'add-wall':
                this.addWall(x, y);
                break;
            case 'remove-wall':
                this.removeWall(x, y);
                break;
            case 'set-player-start':
                this.setPlayerStart(x, y);
                break;
            case 'draw-path':
                this.addPathPoint(x, y);
                break;
            case 'add-enemy':
                if (this.selectedEnemyId) {
                    this.addEnemy(x, y, this.selectedEnemyId);
                }
                break;
            case 'remove-enemy':
                // 单击不删除，使用双击删除
                break;
        }
    }

    /**
     * 获取指定坐标的单元格
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @returns {HTMLElement} 单元格元素
     */
    getCellAt(x, y) {
        return document.querySelector(`.map-grid-cell[data-x="${x}"][data-y="${y}"]`);
    }

    /**
     * 更新地图显示
     */
    updateMapDisplay() {
        // 清除所有单元格的类
        document.querySelectorAll('.map-grid-cell').forEach(cell => {
            cell.className = 'map-grid-cell';
        });

        // 显示墙壁
        for (let y = 0; y < this.currentMap.height; y++) {
            for (let x = 0; x < this.currentMap.width; x++) {
                if (this.currentMap.walls[y][x] === 1) {
                    const cell = this.getCellAt(x, y);
                    if (cell) {
                        cell.classList.add('wall');
                    }
                }
            }
        }

        // 显示玩家起点
        const startCell = this.getCellAt(this.currentMap.playerStart.x, this.currentMap.playerStart.y);
        if (startCell) {
            startCell.classList.add('player-start');
        }

        // 显示敌人
        this.currentMap.enemies.forEach(enemy => {
            const cell = this.getCellAt(enemy.x, enemy.y);
            if (cell) {
                cell.classList.add('enemy');
                cell.dataset.enemyId = enemy.id;
            }
        });

        // 显示路径
        this.currentMap.paths.forEach(path => {
            path.points.forEach(point => {
                const cell = this.getCellAt(point.x, point.y);
                if (cell) {
                    cell.classList.add('path');
                    cell.dataset.pathId = path.id;
                }
            });
        });

        // 显示背景图片
        this.updateBackgroundImage();
    }

    /**
     * 更新背景图片
     */
    updateBackgroundImage() {
        // 移除旧的背景图片
        const oldBackground = this.container.querySelector('.map-background');
        if (oldBackground) {
            this.container.removeChild(oldBackground);
        }

        // 如果有背景图片，添加到容器
        if (this.currentMap && this.currentMap.backgroundImage) {
            const img = document.createElement('img');
            img.src = this.currentMap.backgroundImage;
            img.className = 'map-background';
            img.style.zIndex = '0'; // 确保背景图片在网格下面
            this.container.insertBefore(img, this.container.firstChild);

            // 确保所有网格单元格在背景图片上面
            document.querySelectorAll('.map-grid-cell').forEach(cell => {
                cell.style.zIndex = '1';
            });
        }
    }

    /**
     * 显示背景选择器
     */
    showBackgroundSelector() {
        // 创建文件输入元素
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // 监听文件选择事件
        fileInput.addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (file) {
                try {
                    // 将文件转换为Base64
                    const base64Image = await this.convertFileToBase64(file);

                    // 设置背景图片
                    this.currentMap.backgroundImage = base64Image;

                    // 更新显示
                    this.updateBackgroundImage();

                    console.log('背景图片设置成功');
                } catch (error) {
                    console.error('背景图片设置失败:', error);
                    alert('背景图片设置失败: ' + error.message);
                }
            }

            // 移除文件输入元素
            document.body.removeChild(fileInput);
        });

        // 触发文件选择对话框
        fileInput.click();
    }

    /**
     * 将文件转换为Base64
     * @param {File} file 文件对象
     * @returns {Promise<string>} Base64字符串
     */
    convertFileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = () => {
                resolve(reader.result);
            };

            reader.onerror = (error) => {
                reject(error);
            };

            reader.readAsDataURL(file);
        });
    }

    /**
     * 显示敌人选择器
     */
    async showEnemySelector() {
        try {
            // 获取所有敌人
            const enemies = await this.entityManager.dbManager.getAllData(this.entityManager.dbManager.stores.enemies);

            if (enemies.length === 0) {
                alert('没有可用的敌人，请先创建敌人');
                return;
            }

            // 创建敌人选择表单
            let formHtml = `
                <div class="enemy-selector">
                    <h4>选择要放置的敌人</h4>
                    <div class="form-group">
                        <select id="enemy-selector" class="form-control">
                            ${enemies.map(enemy => `<option value="${enemy.id}">${enemy.name}</option>`).join('')}
                        </select>
                    </div>
                    <div class="form-actions">
                        <button id="select-enemy-btn" class="btn btn-primary">选择</button>
                        <button id="cancel-enemy-select-btn" class="btn btn-secondary">取消</button>
                    </div>
                </div>
            `;

            // 显示模态框
            window.showModal('选择敌人', formHtml);

            // 添加选择按钮事件
            document.getElementById('select-enemy-btn').addEventListener('click', () => {
                const enemyId = parseInt(document.getElementById('enemy-selector').value);
                this.selectedEnemyId = enemyId;
                this.setTool('add-enemy');
                window.closeModal();
            });

            // 添加取消按钮事件
            document.getElementById('cancel-enemy-select-btn').addEventListener('click', window.closeModal);

        } catch (error) {
            console.error('加载敌人失败:', error);
            alert('加载敌人失败: ' + error.message);
        }
    }

    /**
     * 显示地图表单
     * @param {number} mapId 地图ID，如果是新建则为null
     */
    async showMapForm(mapId = null) {
        try {
            let map = null;

            if (mapId) {
                map = await this.mapManager.getMap(mapId);
            }

            // 创建表单HTML
            const formHtml = `
                <form id="map-form" class="entity-form">
                    <input type="hidden" name="id" value="${map ? map.id : ''}">

                    <div class="form-group">
                        <label for="map-name">地图名称</label>
                        <input type="text" id="map-name" name="name" value="${map ? map.name : '新地图'}" required>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">${map ? '更新' : '创建'}</button>
                        <button type="button" class="btn btn-secondary" id="cancel-map-form">取消</button>
                    </div>
                </form>
            `;

            // 显示模态框
            showModal(map ? '编辑地图' : '创建新地图', formHtml);

            // 添加表单提交事件
            document.getElementById('map-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.saveMapForm(e.target);
            });

            // 添加取消按钮事件
            document.getElementById('cancel-map-form').addEventListener('click', closeModal);

        } catch (error) {
            console.error('显示地图表单失败:', error);
            alert('显示地图表单失败: ' + error.message);
        }
    }

    /**
     * 添加墙壁
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    addWall(x, y) {
        // 更新地图数据
        if (this.currentMap.walls[y] && this.currentMap.walls[y][x] !== undefined) {
            this.currentMap.walls[y][x] = 1;

            // 更新显示
            const cell = this.getCellAt(x, y);
            if (cell) {
                cell.classList.add('wall');
            }
        }
    }

    /**
     * 移除墙壁
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    removeWall(x, y) {
        // 更新地图数据
        if (this.currentMap.walls[y] && this.currentMap.walls[y][x] !== undefined) {
            this.currentMap.walls[y][x] = 0;

            // 更新显示
            const cell = this.getCellAt(x, y);
            if (cell) {
                cell.classList.remove('wall');
            }
        }
    }

    /**
     * 清除所有墙壁
     */
    clearWalls() {
        // 确认对话框
        if (confirm('确定要清除所有墙壁吗？')) {
            // 创建新的墙壁数据，保留边界墙壁
            this.currentMap.walls = this.mapManager.createEmptyWallsData(this.mapWidth, this.mapHeight);

            // 更新显示
            this.updateMapDisplay();
        }
    }

    /**
     * 设置玩家起点
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    setPlayerStart(x, y) {
        // 移除旧的起点标记
        const oldStartCell = this.getCellAt(this.currentMap.playerStart.x, this.currentMap.playerStart.y);
        if (oldStartCell) {
            oldStartCell.classList.remove('player-start');
        }

        // 更新地图数据
        this.currentMap.playerStart = { x, y };

        // 更新显示
        const cell = this.getCellAt(x, y);
        if (cell) {
            cell.classList.add('player-start');
        }
    }

    /**
     * 添加敌人
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {number} enemyId 敌人ID
     */
    addEnemy(x, y, enemyId) {
        // 检查该位置是否已有敌人
        const existingEnemyIndex = this.currentMap.enemies.findIndex(e => e.x === x && e.y === y);
        if (existingEnemyIndex !== -1) {
            // 如果已有敌人，则替换
            this.currentMap.enemies[existingEnemyIndex] = { x, y, id: enemyId };
        } else {
            // 否则添加新敌人
            this.currentMap.enemies.push({ x, y, id: enemyId });
        }

        // 更新显示
        const cell = this.getCellAt(x, y);
        if (cell) {
            cell.classList.add('enemy');
            cell.dataset.enemyId = enemyId;
        }
    }

    /**
     * 移除指定位置的敌人
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    removeEnemyAt(x, y) {
        // 查找敌人索引
        const enemyIndex = this.currentMap.enemies.findIndex(e => e.x === x && e.y === y);
        if (enemyIndex !== -1) {
            // 移除敌人
            this.currentMap.enemies.splice(enemyIndex, 1);

            // 更新显示
            const cell = this.getCellAt(x, y);
            if (cell) {
                cell.classList.remove('enemy');
                delete cell.dataset.enemyId;
            }
        }
    }

    /**
     * 清除所有敌人
     */
    clearEnemies() {
        // 确认对话框
        if (confirm('确定要清除所有敌人吗？')) {
            // 清空敌人数组
            this.currentMap.enemies = [];

            // 更新显示
            document.querySelectorAll('.map-grid-cell.enemy').forEach(cell => {
                cell.classList.remove('enemy');
                delete cell.dataset.enemyId;
            });
        }
    }

    /**
     * 添加路径点
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    addPathPoint(x, y) {
        // 如果没有当前路径ID，创建新路径
        if (!this.currentPathId) {
            this.currentPathId = Date.now(); // 使用时间戳作为路径ID
            this.currentMap.paths.push({
                id: this.currentPathId,
                points: []
            });
        }

        // 查找当前路径
        const path = this.currentMap.paths.find(p => p.id === this.currentPathId);
        if (path) {
            // 检查该点是否已在路径中
            const existingPoint = path.points.find(p => p.x === x && p.y === y);
            if (!existingPoint) {
                // 添加新点
                path.points.push({ x, y });

                // 更新显示
                const cell = this.getCellAt(x, y);
                if (cell) {
                    cell.classList.add('path');
                    cell.dataset.pathId = this.currentPathId;
                }
            }
        }
    }

    /**
     * 清除所有路径
     */
    clearPaths() {
        // 确认对话框
        if (confirm('确定要清除所有路径吗？')) {
            // 清空路径数组
            this.currentMap.paths = [];
            this.currentPathId = null;

            // 更新显示
            document.querySelectorAll('.map-grid-cell.path').forEach(cell => {
                cell.classList.remove('path');
                delete cell.dataset.pathId;
            });
        }
    }

    /**
     * 应用网格设置
     */
    applyGridSettings() {
        // 获取设置值
        const gridSize = parseInt(document.getElementById('grid-size').value) || 80;
        const mapWidth = parseInt(document.getElementById('map-width').value) || 16;
        const mapHeight = parseInt(document.getElementById('map-height').value) || 12;

        // 保存当前的地图内容
        const currentWalls = this.currentMap.walls;
        const currentEnemies = this.currentMap.enemies;
        const currentPlayerStart = this.currentMap.playerStart;
        const currentPaths = this.currentMap.paths;
        const currentBackgroundImage = this.currentMap.backgroundImage;
        const currentName = this.currentMap.name;
        const currentId = this.currentMap.id;

        // 更新设置
        this.gridSize = gridSize;
        this.mapWidth = mapWidth;
        this.mapHeight = mapHeight;

        // 清空容器重建网格UI
        this.container.innerHTML = '';
        this.container.style.width = `${this.mapWidth * this.gridSize}px`;
        this.container.style.height = `${this.mapHeight * this.gridSize}px`;

        // 创建网格
        for (let y = 0; y < this.mapHeight; y++) {
            for (let x = 0; x < this.mapWidth; x++) {
                const cell = document.createElement('div');
                cell.className = 'map-grid-cell';
                cell.dataset.x = x;
                cell.dataset.y = y;
                cell.style.width = `${this.gridSize}px`;
                cell.style.height = `${this.gridSize}px`;
                cell.style.left = `${x * this.gridSize}px`;
                cell.style.top = `${y * this.gridSize}px`;

                // 添加单元格事件
                this.addCellEvents(cell);

                this.container.appendChild(cell);
            }
        }

        // 创建地图数据，保留现有的内容
        this.currentMap = {
            name: currentName || '新地图',
            id: currentId, // 保留当前地图ID，如果有的话
            gridSize: this.gridSize,
            width: this.mapWidth,
            height: this.mapHeight,
            // 保留墙壁数据，如果新地图更大，则扩展墙壁数据
            walls: this.extendWallsData(currentWalls, this.mapWidth, this.mapHeight),
            // 保留敌人数据，但需要确保敌人不超出地图范围
            enemies: this.adjustEnemiesPosition(currentEnemies, this.mapWidth, this.mapHeight),
            // 确保玩家起点在地图范围内
            playerStart: this.adjustPlayerStart(currentPlayerStart, this.mapWidth, this.mapHeight),
            // 保留路径数据，但需要确保路径点不超出地图范围
            paths: this.adjustPathsPosition(currentPaths, this.mapWidth, this.mapHeight),
            // 保留背景图片
            backgroundImage: currentBackgroundImage
        };

        // 更新地图显示
        this.updateMapDisplay();

        showNotification('网格设置已更新', 'success');
    }

    /**
     * 扩展墙壁数据以适应新的地图尺寸
     * @param {Array} currentWalls 当前墙壁数据
     * @param {number} newWidth 新地图宽度
     * @param {number} newHeight 新地图高度
     * @returns {Array} 扩展后的墙壁数据
     */
    extendWallsData(currentWalls, newWidth, newHeight) {
        // 如果没有现有的墙壁数据，则创建全新的空墙壁数据
        if (!currentWalls || !currentWalls.length) {
            return this.mapManager.createEmptyWallsData(newWidth, newHeight);
        }

        // 创建新的墙壁数组
        const newWalls = [];
        
        // 填充墙壁数据
        for (let y = 0; y < newHeight; y++) {
            newWalls[y] = [];
            for (let x = 0; x < newWidth; x++) {
                // 如果在原地图范围内，使用原来的值
                if (y < currentWalls.length && x < currentWalls[y].length) {
                    newWalls[y][x] = currentWalls[y][x];
                } else {
                    // 否则设为空地 (0)
                    // 但是给地图边缘加上墙壁 (1)
                    if (x === 0 || y === 0 || x === newWidth - 1 || y === newHeight - 1) {
                        newWalls[y][x] = 1; // 墙壁
                    } else {
                        newWalls[y][x] = 0; // 空地
                    }
                }
            }
        }
        
        return newWalls;
    }

    /**
     * 调整敌人位置以适应新的地图尺寸
     * @param {Array} enemies 敌人数据
     * @param {number} newWidth 新地图宽度
     * @param {number} newHeight 新地图高度
     * @returns {Array} 调整后的敌人数据
     */
    adjustEnemiesPosition(enemies, newWidth, newHeight) {
        if (!enemies || !enemies.length) {
            return [];
        }

        return enemies.filter(enemy => {
            // 只保留在新地图范围内的敌人
            return enemy.x >= 0 && enemy.x < newWidth && enemy.y >= 0 && enemy.y < newHeight;
        });
    }

    /**
     * 调整玩家起点以适应新的地图尺寸
     * @param {Object} playerStart 玩家起点
     * @param {number} newWidth 新地图宽度
     * @param {number} newHeight 新地图高度
     * @returns {Object} 调整后的玩家起点
     */
    adjustPlayerStart(playerStart, newWidth, newHeight) {
        if (!playerStart) {
            return { x: 1, y: 1 };
        }

        // 确保玩家起点在地图范围内
        const x = Math.min(Math.max(1, playerStart.x), newWidth - 2);
        const y = Math.min(Math.max(1, playerStart.y), newHeight - 2);

        return { x, y };
    }

    /**
     * 调整路径位置以适应新的地图尺寸
     * @param {Array} paths 路径数据
     * @param {number} newWidth 新地图宽度
     * @param {number} newHeight 新地图高度
     * @returns {Array} 调整后的路径数据
     */
    adjustPathsPosition(paths, newWidth, newHeight) {
        if (!paths || !paths.length) {
            return [];
        }

        return paths.map(path => {
            // 过滤路径点，只保留在新地图范围内的点
            if (path.points && path.points.length) {
                path.points = path.points.filter(point => {
                    return point.x >= 0 && point.x < newWidth && point.y >= 0 && point.y < newHeight;
                });
            }
            return path;
        }).filter(path => path.points && path.points.length > 1); // 至少保留两个点才是有效路径
    }

    /**
     * 保存地图表单
     * @param {HTMLFormElement} form 表单元素
     */
    async saveMapForm(form) {
        try {
            const formData = new FormData(form);
            const mapName = formData.get('name');
            const mapId = formData.get('id');

            if (mapId) {
                // 更新现有地图
                this.currentMap.name = mapName;
                // 确保地图对象有正确的ID
                this.currentMap.id = parseInt(mapId);
                await this.mapManager.updateMap(this.currentMap);
                this.currentMapId = parseInt(mapId);
                showNotification('地图更新成功', 'success');
            } else {
                // 创建新地图
                this.currentMap.name = mapName;
                const id = await this.mapManager.createMap(this.currentMap);
                this.currentMapId = id;
                showNotification('地图创建成功', 'success');
            }

            // 关闭模态框
            closeModal();

            // 加载地图列表
            this.loadMapList();

        } catch (error) {
            console.error('保存地图失败:', error);
            alert('保存地图失败: ' + error.message);
        }
    }

    /**
     * 保存当前地图
     */
    async saveMap() {
        try {
            if (this.currentMapId) {
                // 确保当前地图对象具有正确的ID
                this.currentMap.id = this.currentMapId;
                // 更新现有地图
                await this.mapManager.updateMap(this.currentMap);
                showNotification('地图保存成功', 'success');
            } else {
                // 显示地图表单
                this.showMapForm();
            }
        } catch (error) {
            console.error('保存地图失败:', error);
            alert('保存地图失败: ' + error.message);
        }
    }

    /**
     * 加载地图列表
     */
    async loadMapList() {
        try {
            // 获取所有地图
            const maps = this.mapManager.getAllMaps();

            // 获取地图列表容器
            const mapsList = document.getElementById('maps-list');
            mapsList.innerHTML = '';

            if (maps.length === 0) {
                mapsList.innerHTML = '<p class="text-center">没有地图数据</p>';
                return;
            }

            // 添加地图项
            maps.forEach(map => {
                const mapItem = document.createElement('div');
                mapItem.className = 'map-item';
                if (this.currentMapId === map.id) {
                    mapItem.classList.add('active');
                }

                mapItem.innerHTML = `
                    <div class="map-item-name">${map.name}</div>
                    <div class="map-item-actions">
                        <button class="btn btn-small btn-secondary edit-map" data-id="${map.id}">编辑</button>
                        <button class="btn btn-small btn-danger delete-map" data-id="${map.id}">删除</button>
                    </div>
                `;

                mapsList.appendChild(mapItem);

                // 添加点击事件
                mapItem.addEventListener('click', (e) => {
                    // 如果点击的是按钮，不加载地图
                    if (e.target.tagName === 'BUTTON') {
                        return;
                    }

                    this.loadMap(map.id);
                });
            });

            // 添加编辑按钮事件
            document.querySelectorAll('.edit-map').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const mapId = parseInt(btn.dataset.id);
                    this.showMapForm(mapId);
                });
            });

            // 添加删除按钮事件
            document.querySelectorAll('.delete-map').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const mapId = parseInt(btn.dataset.id);
                    this.confirmDeleteMap(mapId);
                });
            });

        } catch (error) {
            console.error('加载地图列表失败:', error);
            alert('加载地图列表失败: ' + error.message);
        }
    }

    /**
     * 确认删除地图
     * @param {number} mapId 地图ID
     */
    confirmDeleteMap(mapId) {
        if (confirm('确定要删除这个地图吗？此操作不可撤销。')) {
            this.deleteMap(mapId);
        }
    }

    /**
     * 删除地图
     * @param {number} mapId 地图ID
     */
    async deleteMap(mapId) {
        try {
            await this.mapManager.deleteMap(mapId);

            // 如果删除的是当前地图，创建新地图
            if (this.currentMapId === mapId) {
                this.currentMapId = null;
                this.createEmptyMap();
            }

            // 重新加载地图列表
            this.loadMapList();

            showNotification('地图删除成功', 'success');
        } catch (error) {
            console.error('删除地图失败:', error);
            alert('删除地图失败: ' + error.message);
        }
    }

    /**
     * 加载地图
     * @param {number} mapId 地图ID
     */
    async loadMap(mapId) {
        try {
            // 获取地图数据
            const map = this.mapManager.getMap(mapId);
            if (!map) {
                throw new Error('未找到地图');
            }

            // 设置当前地图
            this.currentMap = map;
            this.currentMapId = mapId;

            // 更新网格设置
            this.gridSize = map.gridSize || 80;
            this.mapWidth = map.width || 16;
            this.mapHeight = map.height || 12;

            // 更新网格设置输入框
            document.getElementById('grid-size').value = this.gridSize;
            document.getElementById('map-width').value = this.mapWidth;
            document.getElementById('map-height').value = this.mapHeight;

            // 重新创建网格
            this.container.innerHTML = '';
            this.container.style.width = `${this.mapWidth * this.gridSize}px`;
            this.container.style.height = `${this.mapHeight * this.gridSize}px`;

            // 创建网格
            for (let y = 0; y < this.mapHeight; y++) {
                for (let x = 0; x < this.mapWidth; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'map-grid-cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    cell.style.width = `${this.gridSize}px`;
                    cell.style.height = `${this.gridSize}px`;
                    cell.style.left = `${x * this.gridSize}px`;
                    cell.style.top = `${y * this.gridSize}px`;

                    // 添加单元格事件
                    this.addCellEvents(cell);

                    this.container.appendChild(cell);
                }
            }

            // 更新地图显示
            this.updateMapDisplay();

            // 更新地图列表
            this.loadMapList();

            showNotification(`已加载地图: ${map.name}`, 'success');
        } catch (error) {
            console.error('加载地图失败:', error);
            alert('加载地图失败: ' + error.message);
        }
    }

    /**
     * 测试地图
     */
    testMap() {
        // 保存当前地图
        this.saveMap();

        // 如果没有地图ID，提示先保存
        if (!this.currentMapId) {
            alert('请先保存地图');
            return;
        }

        // 打开游戏页面并传递地图ID
        window.open(`index.html?map=${this.currentMapId}`, '_blank');
    }
}
