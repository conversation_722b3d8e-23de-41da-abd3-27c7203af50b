<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>范围预览测试</title>
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@7.x/dist/pixi.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .preview-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>实体范围预览测试</h1>

        <h2>玩家设置</h2>
        <form id="player-form">
            <div class="form-group">
                <label for="player-name">名称</label>
                <input type="text" id="player-name" name="name" value="测试玩家">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="player-default-width">默认宽度</label>
                    <input type="number" id="player-default-width" name="defaultAnimationWidth" value="60">
                </div>
                <div class="form-group">
                    <label for="player-default-height">默认高度</label>
                    <input type="number" id="player-default-height" name="defaultAnimationHeight" value="60">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="player-collision-width">碰撞框宽度</label>
                    <input type="number" id="player-collision-width" name="collisionBox.width" value="40">
                </div>
                <div class="form-group">
                    <label for="player-collision-height">碰撞框高度</label>
                    <input type="number" id="player-collision-height" name="collisionBox.height" value="40">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="player-attack-range-width">攻击范围宽度</label>
                    <input type="number" id="player-attack-range-width" name="attackRange.width" value="80">
                </div>
                <div class="form-group">
                    <label for="player-attack-range-height">攻击范围高度</label>
                    <input type="number" id="player-attack-range-height" name="attackRange.height" value="80">
                </div>
            </div>

            <button type="button" id="preview-player-ranges" class="btn">预览玩家范围</button>
        </form>

        <h2>敌人设置</h2>
        <form id="enemy-form">
            <div class="form-group">
                <label for="enemy-name">名称</label>
                <input type="text" id="enemy-name" name="name" value="测试敌人">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="enemy-default-width">默认宽度</label>
                    <input type="number" id="enemy-default-width" name="defaultAnimationWidth" value="50">
                </div>
                <div class="form-group">
                    <label for="enemy-default-height">默认高度</label>
                    <input type="number" id="enemy-default-height" name="defaultAnimationHeight" value="50">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="enemy-collision-width">碰撞框宽度</label>
                    <input type="number" id="enemy-collision-width" name="collisionBox.width" value="30">
                </div>
                <div class="form-group">
                    <label for="enemy-collision-height">碰撞框高度</label>
                    <input type="number" id="enemy-collision-height" name="collisionBox.height" value="30">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="enemy-attack-range-width">攻击范围宽度</label>
                    <input type="number" id="enemy-attack-range-width" name="attackRange.width" value="60">
                </div>
                <div class="form-group">
                    <label for="enemy-attack-range-height">攻击范围高度</label>
                    <input type="number" id="enemy-attack-range-height" name="attackRange.height" value="60">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="enemy-alert-range-width">警戒范围宽度</label>
                    <input type="number" id="enemy-alert-range-width" name="alertRange.width" value="120">
                </div>
                <div class="form-group">
                    <label for="enemy-alert-range-height">警戒范围高度</label>
                    <input type="number" id="enemy-alert-range-height" name="alertRange.height" value="120">
                </div>
            </div>

            <button type="button" id="preview-enemy-ranges" class="btn">预览敌人范围</button>
        </form>
    </div>

    <script>
        // 预览功能代码
        document.addEventListener('DOMContentLoaded', function() {
            // 添加预览按钮事件
            document.getElementById('preview-player-ranges').addEventListener('click', function() {
                showEntityRangePreview('player');
            });

            document.getElementById('preview-enemy-ranges').addEventListener('click', function() {
                showEntityRangePreview('enemy');
            });
        });

        /**
         * 显示实体范围预览
         * @param {string} entityType 实体类型 ('player' 或 'enemy')
         */
        function showEntityRangePreview(entityType) {
            try {
                // 获取表单数据
                const form = document.getElementById(`${entityType}-form`);
                if (!form) {
                    console.error('找不到表单');
                    return;
                }

                const formData = new FormData(form);

                // 获取实体数据
                const entityData = {
                    name: formData.get('name') || `${entityType === 'player' ? '玩家' : '敌人'}`,
                    defaultWidth: parseInt(formData.get('defaultAnimationWidth')) || (entityType === 'player' ? 60 : 50),
                    defaultHeight: parseInt(formData.get('defaultAnimationHeight')) || (entityType === 'player' ? 60 : 50),
                    collisionBox: {
                        width: parseInt(formData.get('collisionBox.width')) || (entityType === 'player' ? 40 : 30),
                        height: parseInt(formData.get('collisionBox.height')) || (entityType === 'player' ? 40 : 30)
                    },
                    attackRange: {
                        width: parseInt(formData.get('attackRange.width')) || (entityType === 'player' ? 80 : 60),
                        height: parseInt(formData.get('attackRange.height')) || (entityType === 'player' ? 80 : 60)
                    }
                };

                // 如果是敌人，还要获取警戒范围
                if (entityType === 'enemy') {
                    entityData.alertRange = {
                        width: parseInt(formData.get('alertRange.width')) || 120,
                        height: parseInt(formData.get('alertRange.height')) || 120
                    };
                }

                // 创建预览窗口
                createRangePreviewWindow(entityType, entityData);

            } catch (error) {
                console.error('显示范围预览失败:', error);
                alert('显示范围预览失败');
            }
        }

        /**
         * 创建范围预览窗口
         * @param {string} entityType 实体类型
         * @param {Object} entityData 实体数据
         */
        function createRangePreviewWindow(entityType, entityData) {
            // 移除现有的预览窗口
            const existingPreview = document.getElementById('range-preview-container');
            if (existingPreview) {
                existingPreview.remove();
            }

            // 创建预览容器
            const previewContainer = document.createElement('div');
            previewContainer.id = 'range-preview-container';
            previewContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 500px;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                z-index: 1100;
                overflow: hidden;
            `;

            // 创建预览内容
            previewContainer.innerHTML = \`
                <div style="padding: 1rem 1.5rem; background-color: #34495e; color: white; display: flex; justify-content: space-between; align-items: center;">
                    <h4 style="margin: 0; font-size: 1.1rem;">\${entityData.name} - 范围预览</h4>
                    <button id="close-range-preview" style="background-color: #e74c3c; color: white; padding: 5px 10px; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                </div>
                <div style="padding: 1rem; text-align: center; background-color: #f8f9fa;">
                    <div id="range-preview-pixi-container" style="display: flex; justify-content: center; align-items: center;"></div>
                </div>
                <div style="padding: 1rem 1.5rem; background-color: white; border-top: 1px solid #ddd;">
                    <div style="display: flex; align-items: center; margin-bottom: 0.5rem; font-size: 0.9rem;">
                        <div style="width: 16px; height: 16px; border-radius: 50%; margin-right: 0.5rem; border: 2px solid #e74c3c; background-color: transparent;"></div>
                        <span>碰撞框 (半径: \${Math.max(entityData.collisionBox.width, entityData.collisionBox.height)/2})</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 0.5rem; font-size: 0.9rem;">
                        <div style="width: 16px; height: 16px; border-radius: 50%; margin-right: 0.5rem; border: 2px dashed #f39c12; background-color: transparent;"></div>
                        <span>攻击范围 (半径: \${Math.max(entityData.attackRange.width, entityData.attackRange.height)/2})</span>
                    </div>
                    \${entityType === 'enemy' ? \`
                    <div style="display: flex; align-items: center; margin-bottom: 0.5rem; font-size: 0.9rem;">
                        <div style="width: 16px; height: 16px; border-radius: 50%; margin-right: 0.5rem; border: 2px dashed #9b59b6; background-color: transparent;"></div>
                        <span>警戒范围 (半径: \${Math.max(entityData.alertRange.width, entityData.alertRange.height)/2})</span>
                    </div>
                    \` : ''}
                    <div style="display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem;">
                        <div style="width: 16px; height: 16px; border-radius: 2px; margin-right: 0.5rem; border: 1px solid #ccc; background-color: #3498db;"></div>
                        <span>实体动画 (\${entityData.defaultWidth}x\${entityData.defaultHeight})</span>
                    </div>
                </div>
            \`;

            // 添加到页面
            document.body.appendChild(previewContainer);

            // 绘制预览
            drawRangePreview(entityType, entityData);

            // 添加关闭事件
            document.getElementById('close-range-preview').addEventListener('click', function() {
                previewContainer.remove();
            });
        }

        /**
         * 创建PIXI范围预览（简化版，不包含动画）
         * @param {string} entityType 实体类型
         * @param {Object} entityData 实体数据
         */
        function drawRangePreview(entityType, entityData) {
            try {
                const container = document.getElementById('range-preview-pixi-container');
                if (!container) return;

                // 清理旧的应用
                if (window.rangePreviewApp) {
                    window.rangePreviewApp.destroy(true);
                }

                // 创建PIXI应用
                window.rangePreviewApp = new PIXI.Application({
                    width: 400,
                    height: 300,
                    backgroundColor: 0x333333,
                    antialias: true
                });

                container.innerHTML = '';
                container.appendChild(window.rangePreviewApp.view);

                const centerX = window.rangePreviewApp.screen.width / 2;
                const centerY = window.rangePreviewApp.screen.height / 2;

                // 计算缩放比例
                const maxRange = entityType === 'enemy' ?
                    Math.max(entityData.alertRange.width, entityData.alertRange.height) :
                    Math.max(entityData.attackRange.width, entityData.attackRange.height);
                const scale = Math.min(window.rangePreviewApp.screen.width * 0.8, window.rangePreviewApp.screen.height * 0.8) / maxRange;

                // 先绘制实体（在底层）
                const entitySprite = new PIXI.Graphics();
                entitySprite.beginFill(0x3498db);
                const width = entityData.defaultWidth * scale;
                const height = entityData.defaultHeight * scale;
                entitySprite.drawRect(-width/2, -height/2, width, height);
                entitySprite.x = centerX;
                entitySprite.y = centerY;
                window.rangePreviewApp.stage.addChild(entitySprite);

                // 然后绘制范围（在上层，这样可以清楚看到范围边界）

                // 绘制警戒范围（仅敌人）
                if (entityType === 'enemy') {
                    const alertRadius = Math.max(entityData.alertRange.width, entityData.alertRange.height) / 2 * scale;
                    const alertCircle = new PIXI.Graphics();
                    alertCircle.lineStyle(2, 0x9b59b6, 1);
                    alertCircle.drawCircle(0, 0, alertRadius);
                    alertCircle.x = centerX;
                    alertCircle.y = centerY;
                    window.rangePreviewApp.stage.addChild(alertCircle);
                }

                // 绘制攻击范围
                const attackRadius = Math.max(entityData.attackRange.width, entityData.attackRange.height) / 2 * scale;
                const attackCircle = new PIXI.Graphics();
                attackCircle.lineStyle(2, 0xf39c12, 1);
                attackCircle.drawCircle(0, 0, attackRadius);
                attackCircle.x = centerX;
                attackCircle.y = centerY;
                window.rangePreviewApp.stage.addChild(attackCircle);

                // 绘制碰撞框
                const collisionRadius = Math.max(entityData.collisionBox.width, entityData.collisionBox.height) / 2 * scale;
                const collisionCircle = new PIXI.Graphics();
                collisionCircle.lineStyle(2, 0xe74c3c, 1);
                collisionCircle.drawCircle(0, 0, collisionRadius);
                collisionCircle.x = centerX;
                collisionCircle.y = centerY;
                window.rangePreviewApp.stage.addChild(collisionCircle);

                // 最后绘制中心点（在最上层）
                const centerPoint = new PIXI.Graphics();
                centerPoint.beginFill(0x2c3e50);
                centerPoint.drawCircle(0, 0, 3);
                centerPoint.x = centerX;
                centerPoint.y = centerY;
                window.rangePreviewApp.stage.addChild(centerPoint);

            } catch (error) {
                console.error('创建PIXI预览失败:', error);
                // 回退到文本显示
                const container = document.getElementById('range-preview-pixi-container');
                if (container) {
                    container.innerHTML = `
                        <div style="width: 400px; height: 300px; background-color: #333; display: flex; align-items: center; justify-content: center; color: white; border-radius: 4px;">
                            <div style="text-align: center;">
                                <div style="font-size: 18px; margin-bottom: 10px;">${entityData.name}</div>
                                <div>碰撞框: ${entityData.collisionBox.width}x${entityData.collisionBox.height}</div>
                                <div>攻击范围: ${entityData.attackRange.width}x${entityData.attackRange.height}</div>
                                ${entityType === 'enemy' ? `<div>警戒范围: ${entityData.alertRange.width}x${entityData.alertRange.height}</div>` : ''}
                            </div>
                        </div>
                    `;
                }
            }
        }
    </script>
</body>
</html>
